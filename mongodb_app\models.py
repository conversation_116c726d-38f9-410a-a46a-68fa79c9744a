"""
MongoDB Document Models for RTRDA Project

These models represent MongoDB document structures and provide
validation and serialization methods following Django patterns.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from bson import ObjectId
from utils.mongodb_crud import MongoDBCRUD


class BaseMongoDocument:
    """
    Base class for MongoDB documents with common fields and methods.
    """

    def __init__(self, **kwargs):
        self._id = kwargs.get('_id')

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary."""
        data = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                if isinstance(value, ObjectId):
                    data[key] = str(value)
                elif isinstance(value, datetime):
                    data[key] = value.isoformat()
                else:
                    data[key] = value

        if self._id:
            data['_id'] = str(self._id)

        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create document instance from dictionary."""
        return cls(**data)


class MongoUser(BaseMongoDocument):
    """
    MongoDB User document model.
    Example document structure for user data stored in MongoDB.
    """

    collection_name = 'users'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.username = kwargs.get('username', '')
        self.email = kwargs.get('email', '')
        self.first_name = kwargs.get('first_name', '')
        self.last_name = kwargs.get('last_name', '')
        self.phone_number = kwargs.get('phone_number', '')
        self.user_type = kwargs.get('user_type', 'regular')
        self.is_active = kwargs.get('is_active', True)
        self.profile_data = kwargs.get('profile_data', {})
        self.preferences = kwargs.get('preferences', {})
        self.last_login = kwargs.get('last_login')

    def validate(self) -> List[str]:
        """Validate document data."""
        errors = []

        if not self.username:
            errors.append("Username is required")

        if not self.email:
            errors.append("Email is required")
        elif '@' not in self.email:
            errors.append("Invalid email format")

        if self.user_type not in ['admin', 'regular', 'guest']:
            errors.append("Invalid user type")

        return errors

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()


class MongoLog(BaseMongoDocument):
    """
    MongoDB Log document model.
    Example document structure for application logs stored in MongoDB.
    """

    collection_name = 'logs'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.level = kwargs.get('level', 'INFO')
        self.message = kwargs.get('message', '')
        self.module = kwargs.get('module', '')
        self.function = kwargs.get('function', '')
        self.line_number = kwargs.get('line_number')
        self.user_id = kwargs.get('user_id')
        self.session_id = kwargs.get('session_id')
        self.ip_address = kwargs.get('ip_address')
        self.user_agent = kwargs.get('user_agent')
        self.request_method = kwargs.get('request_method')
        self.request_path = kwargs.get('request_path')
        self.response_status = kwargs.get('response_status')
        self.execution_time = kwargs.get('execution_time')
        self.extra_data = kwargs.get('extra_data', {})

    def validate(self) -> List[str]:
        """Validate document data."""
        errors = []

        if not self.message:
            errors.append("Message is required")

        if self.level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            errors.append("Invalid log level")

        return errors


class MongoAnalytics(BaseMongoDocument):
    """
    MongoDB Analytics document model.
    Example document structure for analytics data stored in MongoDB.
    """

    collection_name = 'analytics'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.event_type = kwargs.get('event_type', '')
        self.event_name = kwargs.get('event_name', '')
        self.user_id = kwargs.get('user_id')
        self.session_id = kwargs.get('session_id')
        self.timestamp = kwargs.get('timestamp', datetime.utcnow())
        self.properties = kwargs.get('properties', {})
        self.page_url = kwargs.get('page_url')
        self.referrer = kwargs.get('referrer')
        self.ip_address = kwargs.get('ip_address')
        self.user_agent = kwargs.get('user_agent')
        self.device_info = kwargs.get('device_info', {})
        self.location_info = kwargs.get('location_info', {})

    def validate(self) -> List[str]:
        """Validate document data."""
        errors = []

        if not self.event_type:
            errors.append("Event type is required")

        if not self.event_name:
            errors.append("Event name is required")

        return errors


class MongoConfiguration(BaseMongoDocument):
    """
    MongoDB Configuration document model.
    Example document structure for application configuration stored in MongoDB.
    """

    collection_name = 'configurations'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.key = kwargs.get('key', '')
        self.value = kwargs.get('value')
        self.data_type = kwargs.get('data_type', 'string')
        self.category = kwargs.get('category', 'general')
        self.description = kwargs.get('description', '')
        self.is_sensitive = kwargs.get('is_sensitive', False)
        self.is_active = kwargs.get('is_active', True)
        self.environment = kwargs.get('environment', 'production')

    def validate(self) -> List[str]:
        """Validate document data."""
        errors = []

        if not self.key:
            errors.append("Key is required")

        if self.value is None:
            errors.append("Value is required")

        if self.data_type not in ['string', 'integer', 'float', 'boolean', 'json', 'list']:
            errors.append("Invalid data type")

        return errors

class ComponentSrc(BaseMongoDocument):
    collection_name = 'componentSrc'

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.componentId = kwargs.get('componentId', '')
        self.src = kwargs.get('src', '')

    def validate(self) -> List[str]:
        """Validate document data."""
        errors = []

        if not self.componentId:
            errors.append("Component ID is required")

        if not self.src:
            errors.append("Source is required")

        return errors
