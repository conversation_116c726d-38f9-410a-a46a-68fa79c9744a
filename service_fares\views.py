from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Servicefares, Route
from .serializers import ServicefaresSerializer, RouteSerializer


@extend_schema(
    tags=["Service Fares"]
)
class ServicefaresViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Servicefares model providing CRUD operations.
    """
    queryset = Servicefares.objects.all()
    serializer_class = ServicefaresSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['route__id','originStation','destinationStation','fareType','serviceType','fareAmount']
    search_fields = ['route__id','originStation','destinationStation','fareType','serviceType','fareAmount']
    
    def list(self, request, *args, **kwargs):
        route__id = request.query_params.get('route__id')
        originStation = request.query_params.get('originStation')
        destinationStation = request.query_params.get('destinationStation')
        fareType = request.query_params.get('fareType')
        serviceType = request.query_params.get('serviceType')
        fareAmount = request.query_params.get('fareAmount')
        ordering = request.query_params.get('ordering')     
        queryset = self.get_queryset()
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if originStation:
            queryset = queryset.filter(originStation__icontains=originStation)
        if destinationStation:
            queryset = queryset.filter(destinationStation__icontains=destinationStation)
        if fareType:
            queryset = queryset.filter(fareType=fareType)
        if serviceType:
            queryset = queryset.filter(serviceType=serviceType)
        if fareAmount:    
            queryset = queryset.filter(fareAmount=fareAmount)  
        if ordering:
            queryset = queryset.order_by(ordering)           
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return super().list(request, *args, **kwargs)


@extend_schema(
    tags=["Route"]
)
class RouteViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Route model providing CRUD operations.
    """
    queryset = Route.objects.all()
    serializer_class = RouteSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['masTrainType__id','masRailwayLine__id','name','origin','destination']

    def list(self, request, *args, **kwargs):
        masTrainType__id = request.query_params.get('masTrainType__id')
        masRailwayLine__id = request.query_params.get('masRailwayLine__id')
        name = request.query_params.get('name')
        origin = request.query_params.get('origin')
        destination = request.query_params.get('destination')
        ordering = request.query_params.get('ordering')
        queryset = self.get_queryset()
        if masTrainType__id:
            queryset = queryset.filter(masTrainType__id=masTrainType__id)
        if masRailwayLine__id:
            queryset = queryset.filter(masRailwayLine__id=masRailwayLine__id)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if origin:
            queryset = queryset.filter(origin__icontains=origin)
        if destination:
            queryset = queryset.filter(destination__icontains=destination)
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        return super().list(request, *args, **kwargs)
