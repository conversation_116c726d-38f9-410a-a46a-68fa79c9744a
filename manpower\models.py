from django.db import models
from RTRDA.model import BaseModel


class ManpowerQualifications(BaseModel):
    code = models.CharField(db_column='Code', max_length=100, db_collation='Thai_CI_AI')
    name = models.CharField(db_column='Name', max_length=500, db_collation='Thai_CI_AI')
    organization = models.CharField(db_column='Organization', max_length=500, db_collation='Thai_CI_AI')
    qualification = models.CharField(db_column='Qualification', max_length=1, db_collation='Thai_CI_AI')
    fieldOfStudy = models.Char<PERSON>ield(db_column='FieldOfStudy', max_length=500, db_collation='Thai_CI_AI')
    experienceYears = models.IntegerField(db_column='ExperienceYears')
    skill = models.Char<PERSON>ield(db_column='Skill', max_length=1000, db_collation='Thai_CI_AI')
    isCertification = models.BooleanField(db_column='IsCertification')
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'ManpowerQualifications'

