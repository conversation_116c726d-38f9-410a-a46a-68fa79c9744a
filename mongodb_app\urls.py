"""
URL Configuration for MongoDB App

This module defines URL patterns for MongoDB API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ComponentSrcViewSet,
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'component-srcs', ComponentSrcViewSet, basename='component-srcs')

urlpatterns = [
    path('', include(router.urls)),
]
