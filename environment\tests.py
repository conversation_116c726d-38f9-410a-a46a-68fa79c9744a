from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Environment
from .serializers import EnvironmentSerializer
from mas.models import MasProvince, MasDistrict, MasGeography
from service_fares.models import Route
from mas.models import MasTrainType, MasRailwayLine, MasStandardAgency
from datetime import datetime

User = get_user_model()


class EnvironmentSerializerTest(TestCase):
    """Test cases for Environment serializer"""

    def setUp(self):
        # Create test data
        self.geography = MasGeography.objects.create(name="Test Geography")
        self.province = MasProvince.objects.create(
            name="Test Province",
            masGeography=self.geography
        )
        self.district = MasDistrict.objects.create(
            name="Test District",
            masProvince=self.province,
            twoDigit="01",
            fourDigit="0101"
        )

        # Create standard agency for train type
        self.standard_agency = MasStandardAgency.objects.create(
            name="Test Agency",
            code="TA"
        )

        # Create train type and railway line for route
        self.train_type = MasTrainType.objects.create(
            name="Test Train Type",
            masStandardAgency=self.standard_agency
        )
        self.railway_line = MasRailwayLine.objects.create(
            name="Test Railway Line"
        )

        self.route = Route.objects.create(
            name="Test Route",
            origin="Origin Station",
            destination="Destination Station",
            masTrainType=self.train_type,
            masRailwayLine=self.railway_line
        )

        self.environment_data = {
            'route': self.route,
            'masProvince': self.province,
            'masDistrict': self.district,
            'parameter': 'Temperature',
            'value': 25.5,
            'unit': 'Celsius',
            'measurementDate': datetime.now(),
            'complianceStandard': 'ISO 14001',
            'remark': 'Test measurement'
        }

    def test_environment_serializer_valid_data(self):
        """Test serializer with valid data"""
        environment = Environment.objects.create(**self.environment_data)
        serializer = EnvironmentSerializer(environment)

        self.assertEqual(serializer.data['parameter'], 'Temperature')
        self.assertEqual(serializer.data['value'], 25.5)
        self.assertEqual(serializer.data['unit'], 'Celsius')
        self.assertIsNotNone(serializer.data['route'])
        self.assertIsNotNone(serializer.data['masProvince'])
        self.assertIsNotNone(serializer.data['masDistrict'])


class EnvironmentViewSetTest(APITestCase):
    """Test cases for Environment ViewSet"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test data (same as serializer test)
        self.geography = MasGeography.objects.create(name="Test Geography")
        self.province = MasProvince.objects.create(
            name="Test Province",
            masGeography=self.geography
        )
        self.district = MasDistrict.objects.create(
            name="Test District",
            masProvince=self.province,
            twoDigit="01",
            fourDigit="0101"
        )

        self.standard_agency = MasStandardAgency.objects.create(
            name="Test Agency",
            code="TA"
        )

        self.train_type = MasTrainType.objects.create(
            name="Test Train Type",
            masStandardAgency=self.standard_agency
        )
        self.railway_line = MasRailwayLine.objects.create(
            name="Test Railway Line"
        )

        self.route = Route.objects.create(
            name="Test Route",
            origin="Origin Station",
            destination="Destination Station",
            masTrainType=self.train_type,
            masRailwayLine=self.railway_line
        )

        self.environment = Environment.objects.create(
            route=self.route,
            masProvince=self.province,
            masDistrict=self.district,
            parameter='Temperature',
            value=25.5,
            unit='Celsius',
            measurementDate=datetime.now(),
            complianceStandard='ISO 14001',
            remark='Test measurement'
        )

    def test_get_environment_list(self):
        """Test GET request to environment list"""
        url = reverse('environment-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertContains(response, 'Temperature')

    def test_get_environment_detail(self):
        """Test GET request to environment detail"""
        url = reverse('environment-detail', kwargs={'pk': self.environment.pk})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['parameter'], 'Temperature')
        self.assertEqual(response.data['value'], 25.5)
