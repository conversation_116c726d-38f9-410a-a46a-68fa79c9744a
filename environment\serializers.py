from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Environment
from mas.serializers import MasProvinceSerializer, MasDistrictSerializer
from service_fares.serializers import RouteSerializer
from mas.models import MasProvince, MasDistrict
from service_fares.models import Route


class EnvironmentSerializer(BaseModelSerializer):
    """
    Serializer for Environment model
    """
    route = RouteSerializer(read_only=True)
    masProvince = MasProvinceSerializer(read_only=True)
    masDistrict = MasDistrictSerializer(read_only=True)
    
    # Write fields for foreign keys
    routeId = serializers.PrimaryKeyRelatedField(
        source='route',
        queryset=Route.objects.all(),
        write_only=True
    )
    masProvinceId = serializers.PrimaryKeyRelatedField(
        source='masProvince',
        queryset=MasProvince.objects.all(),
        write_only=True
    )
    masDistrictId = serializers.PrimaryKeyRelatedField(
        source='masDistrict',
        queryset=MasDistrict.objects.all(),
        write_only=True
    )
    
    class Meta:
        model = Environment
        fields = '__all__'
