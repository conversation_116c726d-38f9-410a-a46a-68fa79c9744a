from rest_framework import serializers
from course.models import Course
from research.models import Research

class PowerBICourseSerializer(serializers.ModelSerializer):
    """Serializer for Power BI course data with specific field mapping"""
    university = serializers.CharField(source='universityName')
    faculty = serializers.CharField(source='facultyName')
    department = serializers.CharField()
    courseNameTh = serializers.CharField(source='programTh')
    courseNameEn = serializers.CharField(source='programEn')
    courseType = serializers.CharField(source='masCourseType.name')
    courseDetails = serializers.CharField(source='detail')
    studentsPerYear = serializers.SerializerMethodField()
    courseWebsite = serializers.CharField(source='link')
    coordinator = serializers.CharField(source='contactName')
    coordinatorEmail = serializers.CharField(source='contactEmail')

    def get_studentsPerYear(self, obj):
        """Convert student number range to numeric value (midpoint of range)"""
        student_count = obj.numberOfStudent
        if not student_count:
            return None

        # Handle range values like '150-260'
        if '-' in str(student_count):
            try:
                parts = str(student_count).split('-')
                if len(parts) == 2:
                    min_val = int(parts[0].strip())
                    max_val = int(parts[1].strip())
                    return (min_val + max_val) // 2  # Return midpoint
            except (ValueError, IndexError):
                pass

        # Handle single numeric values
        try:
            return int(student_count)
        except (ValueError, TypeError):
            return None

    class Meta:
        model = Course
        fields = [
            'university', 'faculty', 'department', 'courseNameTh',
            'courseNameEn', 'courseType', 'courseDetails', 'studentsPerYear',
            'courseWebsite', 'coordinator', 'coordinatorEmail'
        ]

class PowerBIResearchSerializer(serializers.ModelSerializer):
    projectCode = serializers.CharField()
    projectName = serializers.CharField()
    researcher = serializers.CharField()
    researchOrganization = serializers.CharField()
    year = serializers.IntegerField()
    fullReportLink = serializers.CharField()

    class Meta:
        model = Research
        fields = [
            'projectCode', 
            'projectName', 
            'researcher', 
            'researchOrganization',
            'year', 
            'fullReportLink'
        ]
