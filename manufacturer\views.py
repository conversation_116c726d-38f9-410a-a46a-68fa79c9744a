from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from .models import Manufacturer
from .serializers import ManufacturerSerializer
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny
from components.models import ManufacturerComponent
from django.http import HttpResponse
import pandas as pd
import io
from datetime import datetime
from mas.models import MasProvince, MasDistrict, MasSubdistrict, MasStandardCategory
import urllib.parse
from rest_framework.response import Response
from utils.util import convert_str_to_bool
from components.serializers import ManufacturerComponentSerializer
from rest_framework import status
from users.models import User
from manufacturer.models import ManufacturerMasStandardCategory
from manufacturer.serializers import ManufacturerMasStandardCategorySerializer


@extend_schema(
    tags=["Manufacturer"]
)
class ManufacturerViewSet(viewsets.ModelViewSet):
    queryset = Manufacturer.objects.all()
    serializer_class = ManufacturerSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = [
        'id',
        'name',
        'address',
        'masGeography__id',
        'masProvince__id',
        'masDistrict__id',
        'masSubdistrict__id',
        'zipcode',
        'phoneNumber',
        'email',
        'latitude',
        'longitude',
        'status',
    ]

    def list(self, request, *args, **kwargs):
        id = request.query_params.get('id')
        name = request.query_params.get('name')
        address = request.query_params.get('address')
        masGeography__id = request.query_params.get('masGeography__id')
        masProvince__id = request.query_params.get('masProvince__id')
        masDistrict__id = request.query_params.get('masDistrict__id')
        masSubdistrict__id = request.query_params.get('masSubdistrict__id')
        zipcode = request.query_params.get('zipcode')
        phoneNumber = request.query_params.get('phoneNumber')
        email = request.query_params.get('email')
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        component__id = request.query_params.get('component__id')
        status = request.query_params.get('status')
        standardTypes = request.query_params.get('standardTypes')
        ordering = request.query_params.get('ordering')

        queryset = self.get_queryset()
        if id:
            queryset = queryset.filter(id=id)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if address:
            queryset = queryset.filter(address__icontains=address)
        if masGeography__id:
            queryset = queryset.filter(masGeography__id=masGeography__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if masSubdistrict__id:
            queryset = queryset.filter(masSubdistrict__id=masSubdistrict__id)
        if zipcode:
            queryset = queryset.filter(zipcode__icontains=zipcode)
        if phoneNumber:
            queryset = queryset.filter(phoneNumber__icontains=phoneNumber)
        if email:
            queryset = queryset.filter(email__icontains=email)
        if latitude:
            queryset = queryset.filter(latitude__icontains=latitude)
        if longitude:
            queryset = queryset.filter(longitude__icontains=longitude)
        if component__id:
            manufacturerComponents = ManufacturerComponent.objects.filter(component__id=component__id)
            queryset = queryset.filter(id__in=manufacturerComponents.values_list('manufacturer__id', flat=True))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if standardTypes:
            standardTypeList = [int(standardType) for standardType in standardTypes.split(',')]
            manufacturerMasStandardCategories = ManufacturerMasStandardCategory.objects.filter(masStandardCategory__id__in=standardTypeList)
            queryset = queryset.filter(id__in=manufacturerMasStandardCategories.values_list('manufacturer__id', flat=True))
        if ordering:
            queryset = queryset.order_by(ordering)
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            for item in serializer.data:
                manufacturer_components = ManufacturerComponent.objects.filter(manufacturer__id=item['id'])
                item['manufacturerComponents'] = ManufacturerComponentSerializer(manufacturer_components, many=True).data
                manufacturerMasStandardCategories = ManufacturerMasStandardCategory.objects.filter(manufacturer__id=item['id'])
                item['manufacturerMasStandardCategories'] = ManufacturerMasStandardCategorySerializer(manufacturerMasStandardCategories, many=True).data
                
            return self.get_paginated_response(serializer.data)
        serializer = self.gest_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        manufacturer = serializer.save()
        standardTypes = request.data.get('standardTypes', [])
        for standardType in standardTypes:
            ManufacturerMasStandardCategory.objects.create(manufacturer=manufacturer, masStandardCategory=MasStandardCategory.objects.get(id=standardType))
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        manufacturer = serializer.save()
        standardTypes = request.data.get('standardTypes', [])
        
        existing_categories = set(ManufacturerMasStandardCategory.objects.filter(manufacturer=manufacturer).values_list('masStandardCategory__id', flat=True))
        new_categories = set(int(standardType) for standardType in standardTypes)
        
        categories_to_delete = existing_categories - new_categories
        if categories_to_delete:
            ManufacturerMasStandardCategory.objects.filter(manufacturer=manufacturer, masStandardCategory__id__in=categories_to_delete).delete()
        
        categories_to_create = new_categories - existing_categories
        for standardType in categories_to_create:
            ManufacturerMasStandardCategory.objects.create(manufacturer=manufacturer, masStandardCategory=MasStandardCategory.objects.get(id=standardType))
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data
        manufacturerMasStandardCategories = ManufacturerMasStandardCategory.objects.filter(manufacturer=instance)
        data['manufacturerMasStandardCategories'] = ManufacturerMasStandardCategorySerializer(manufacturerMasStandardCategories, many=True).data
        return Response(data)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        Download manufacturer data as Excel file
        Request Body:
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        """

        ids = request.data.get('ids')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)

        for item in queryset:
            try:
                province = MasProvince.objects.get(id=item.masProvince.id)
                item.province = province.name
                district = MasDistrict.objects.get(id=item.masDistrict.id)
                item.district = district.name
                subdistrict = MasSubdistrict.objects.get(
                    id=item.masSubdistrict.id)
                item.subdistrict = subdistrict.name
            except MasProvince.DoesNotExist:
                item.province = None
            except MasDistrict.DoesNotExist:
                item.district = None
            except MasSubdistrict.DoesNotExist:
                item.subdistrict = None

        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'ชื่อผู้ประกอบการ': item.name,
                'ที่อยู่': item.address,
                'จังหวัด': item.province,
                'อำเภอ': item.district,
                'ตำบล': item.subdistrict,
                'รหัสไปรษณีย์': item.zipcode,
                'เบอร์ติดต่อ': item.phoneNumber,
                'อีเมล': item.email,
            })

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้ผลิต/โรงงาน'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)

            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })

            # Add the Thai header text
            worksheet.merge_range('A1:H1', sheet_name, header_format)

            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }

            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"

            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:H2', thai_date, date_format)

            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(
                    str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)

        excel_file.seek(0)

        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Encode the Thai filename for better cross-browser compatibility
        # Changed '/' to '_' to avoid path issues
        filename = f'{sheet_name.replace("/", "_")}.xlsx'

        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

        response['Content-Disposition'] = content_disposition

        return response

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลผู้ผลิต/โรงงาน</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of manufacturer IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        address = request.data.get('address')
        masGeography__id = request.data.get('masGeography__id')
        masProvince__id = request.data.get('masProvince__id')
        masDistrict__id = request.data.get('masDistrict__id')
        masSubdistrict__id = request.data.get('masSubdistrict__id')
        zipcode = request.data.get('zipcode')
        phoneNumber = request.data.get('phoneNumber')
        email = request.data.get('email')
        latitude = request.data.get('latitude')
        longitude = request.data.get('longitude')
        component__id = request.data.get('component__id')
        status = request.data.get('status')
        standardTypes = request.data.get('standardTypes')
         
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if address:
            queryset = queryset.filter(address__icontains=address)
        if masGeography__id:
            queryset = queryset.filter(masGeography__id=masGeography__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if masSubdistrict__id:
            queryset = queryset.filter(masSubdistrict__id=masSubdistrict__id)
        if zipcode:
            queryset = queryset.filter(zipcode__icontains=zipcode)
        if phoneNumber:
            queryset = queryset.filter(phoneNumber__icontains=phoneNumber)
        if email:
            queryset = queryset.filter(email__icontains=email)
        if latitude:
            queryset = queryset.filter(latitude__icontains=latitude)
        if longitude:
            queryset = queryset.filter(longitude__icontains=longitude)
        if component__id:
            manufacturerComponents = ManufacturerComponent.objects.filter(component__id=component__id)
            queryset = queryset.filter(id__in=manufacturerComponents.values_list('manufacturer__id', flat=True))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if standardTypes:
            standardTypeList = [int(standardType) for standardType in standardTypes.split(',')]
            manufacturerMasStandardCategories = ManufacturerMasStandardCategory.objects.filter(masStandardCategory__id__in=standardTypeList)
            queryset = queryset.filter(id__in=manufacturerMasStandardCategories.values_list('manufacturer__id', flat=True))
          
        for item in queryset:
            try:
                masProvince = MasProvince.objects.get(id=item.masProvince.id)
                item.provinceName = masProvince.name
                masDistrict = MasDistrict.objects.get(id=item.masDistrict.id)
                item.districtName = masDistrict.name
                masSubdistrict = MasSubdistrict.objects.get(id=item.masSubdistrict.id)
                item.subdistrictName = masSubdistrict.name
            except MasProvince.DoesNotExist:
                item.provinceName = None
            except MasDistrict.DoesNotExist:
                item.districtName = None
            except MasSubdistrict.DoesNotExist:
                item.subdistrictName = None
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)
            
        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อผู้ประกอบการ': item.name,
                'ที่อยู่': item.address,
                'แขวง/ตำบล': item.subdistrictName,
                'เขต/อำเภอ': item.districtName,
                'จังหวัด': item.provinceName,
                'รหัสไปรษณีย์': item.zipcode,
                'เบอร์ติดต่อ': item.phoneNumber,
                'อีเมล': item.email,
                'ละติจูด': item.latitude,
                'ลองจิจูด': item.longitude,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้ผลิต/โรงงาน'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)

            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })

            # Add the Thai header text
            worksheet.merge_range('A1:N1', sheet_name, header_format)

            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }

            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"

            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:N2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'

        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"

        response['Content-Disposition'] = content_disposition

        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
