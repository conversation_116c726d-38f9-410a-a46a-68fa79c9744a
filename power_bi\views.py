from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from course.models import Course
from .serializers import PowerBICourseSerializer, PowerBIResearchSerializer
from research.models import Research


@extend_schema(
    tags=["Power BI"],
    description="""
    Retrieve course data for Power BI integration.

    **Authentication Required**: This endpoint requires API key authentication.

    **Headers Required**:
    - `x-api-key`: Your PowerBI API key

    **Example Request**:
    ```
    GET /api/powerbi/course/
    Headers:
        x-api-key: your-api-key-here
    ```

    **Error Responses**:
    - `401 Unauthorized`: Missing or invalid API key
    - `500 Internal Server Error`: Server error
    """,
    parameters=[
        OpenApiParameter(
            name='x-api-key',
            type=str,
            location=OpenApiParameter.HEADER,
            required=True,
            description='API key for PowerBI authentication'
        ),
    ],
    responses={
        200: PowerBICourseSerializer(many=True),
        401: {
            "description": "Authentication failed",
            "content": {
                "application/json": {
                    "example": {
                        "status": "error",
                        "code": "missing_api_key",
                        "message": "API key is required",
                        "version": "v.1.1.0"
                    }
                }
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_course(request):
    try:
        courses = Course.objects.select_related('masCourseType').order_by('-id')
        total = courses.count()

        return Response({
            "data": PowerBICourseSerializer(courses, many=True).data,
            "total": total,
        }, status=200)
    except Exception as e:
        return Response({
            "error": "Error",
            "message": str(e)
        }, status=500)


@extend_schema(
    tags=["Power BI"],
    description="""
    Retrieve research data for Power BI integration.

    **Authentication Required**: This endpoint requires API key authentication. 
    """,
    parameters=[
        OpenApiParameter(
            name='x-api-key',
            type=str,
            location=OpenApiParameter.HEADER,
            required=True,
            description='API key for PowerBI authentication'
        ),
    ],
    responses={
        200: PowerBIResearchSerializer(many=True),
        401: {
            "description": "Authentication failed",
            "content": {
                "application/json": {
                    "example": {
                        "status": "error",
                        "code": "missing_api_key",
                        "message": "API key is required",
                        "version": "v.1.1.0"
                    }
                }
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_research(request):
    try:
        # Execute the complex query equivalent to the provided SQL
        # This creates a result set similar to:
        # SELECT r.Id, r.ProjectCode, r.ProjectName, r.Year, r.Link,
        #        re.Name as ResearcherName, ra.Name as ResearchAgency
        # FROM Research r
        # INNER JOIN ResearchResearcher rr ON r.Id = rr.ResearchId
        # INNER JOIN Researcher re ON re.Id = rr.ResearcherId
        # INNER JOIN ResearchResearchAgency rra ON r.Id = rra.ResearchId
        # INNER JOIN ResearchAgency ra ON rra.ResearchAgencyId = ra.Id
        # ORDER BY Id DESC

        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT r.[projectCode],
                       r.[projectName],
                       r.[year],
                       r.[link],
                       re.Name as researcherName,
                       ra.Name as researchAgency
                FROM [rtrda].[dbo].[Research] r
                         INNER JOIN ResearchResearcher rr ON r.id = rr.researchId
                         INNER JOIN Researcher re ON re.id = rr.researcherId
                         INNER JOIN ResearchResearchAgency rra ON r.id = rra.researchId
                         INNER JOIN ResearchAgency ra ON rra.researchAgencyId = ra.id
                ORDER BY r.id DESC
            """)

            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        total = len(results)

        return Response({
            "data": results,
            "total": total,
        }, status=200)
    except Exception as e:
        return Response({
            "error": "Error",
            "message": str(e)
        }, status=500)

