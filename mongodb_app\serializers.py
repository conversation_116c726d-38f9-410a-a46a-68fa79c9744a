"""
MongoDB Document Serializers for RTRDA Project

These serializers handle validation and serialization of MongoDB documents
following Django REST Framework patterns.
"""

from rest_framework import serializers
from datetime import datetime
from typing import Dict, Any
import os
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from .models import MongoUser, MongoLog, MongoAnalytics, MongoConfiguration, ComponentSrc


class BaseMongoSerializer(serializers.Serializer):
    """
    Base serializer for MongoDB documents with common fields.
    """
    
    _id = serializers.CharField(read_only=True)

    def get_model_class(self):
        """Get the model class for this serializer."""
        raise NotImplementedError("Subclasses must implement get_model_class()")


class MongoUserSerializer(BaseMongoSerializer):
    """
    Serializer for MongoUser documents.
    """
    
    username = serializers.CharField(max_length=150, required=True)
    email = serializers.EmailField(required=True)
    first_name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    last_name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    phone_number = serializers.CharField(max_length=20, required=False, allow_blank=True)
    user_type = serializers.ChoiceField(
        choices=['admin', 'regular', 'guest'],
        default='regular'
    )
    is_active = serializers.BooleanField(default=True)
    profile_data = serializers.JSONField(required=False, default=dict)
    preferences = serializers.JSONField(required=False, default=dict)
    last_login = serializers.DateTimeField(required=False, allow_null=True)
    full_name = serializers.CharField(read_only=True)
    
    def get_model_class(self):
        return MongoUser
    
    def validate_username(self, value):
        """Validate username."""
        if len(value) < 3:
            raise serializers.ValidationError("Username must be at least 3 characters long")
        return value
    
    def validate_email(self, value):
        """Validate email."""
        if not value or '@' not in value:
            raise serializers.ValidationError("Invalid email format")
        return value.lower()
    
    def to_representation(self, instance):
        """Convert instance to representation."""
        if hasattr(instance, 'to_dict'):
            data = instance.to_dict()
            data['full_name'] = instance.full_name
            return data
        return super().to_representation(instance)


class MongoLogSerializer(BaseMongoSerializer):
    """
    Serializer for MongoLog documents.
    """
    
    level = serializers.ChoiceField(
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO'
    )
    message = serializers.CharField(required=True)
    module = serializers.CharField(max_length=200, required=False, allow_blank=True)
    function = serializers.CharField(max_length=200, required=False, allow_blank=True)
    line_number = serializers.IntegerField(required=False, allow_null=True)
    user_id = serializers.CharField(required=False, allow_blank=True)
    session_id = serializers.CharField(required=False, allow_blank=True)
    ip_address = serializers.IPAddressField(required=False, allow_null=True)
    user_agent = serializers.CharField(required=False, allow_blank=True)
    request_method = serializers.CharField(max_length=10, required=False, allow_blank=True)
    request_path = serializers.CharField(required=False, allow_blank=True)
    response_status = serializers.IntegerField(required=False, allow_null=True)
    execution_time = serializers.FloatField(required=False, allow_null=True)
    extra_data = serializers.JSONField(required=False, default=dict)
    
    def get_model_class(self):
        return MongoLog
    
    def validate_message(self, value):
        """Validate log message."""
        if not value or not value.strip():
            raise serializers.ValidationError("Log message cannot be empty")
        return value.strip()
    
    def validate_response_status(self, value):
        """Validate HTTP response status."""
        if value is not None and (value < 100 or value > 599):
            raise serializers.ValidationError("Invalid HTTP status code")
        return value


class MongoAnalyticsSerializer(BaseMongoSerializer):
    """
    Serializer for MongoAnalytics documents.
    """
    
    event_type = serializers.CharField(max_length=100, required=True)
    event_name = serializers.CharField(max_length=200, required=True)
    user_id = serializers.CharField(required=False, allow_blank=True)
    session_id = serializers.CharField(required=False, allow_blank=True)
    timestamp = serializers.DateTimeField(default=datetime.utcnow)
    properties = serializers.JSONField(required=False, default=dict)
    page_url = serializers.URLField(required=False, allow_blank=True)
    referrer = serializers.URLField(required=False, allow_blank=True)
    ip_address = serializers.IPAddressField(required=False, allow_null=True)
    user_agent = serializers.CharField(required=False, allow_blank=True)
    device_info = serializers.JSONField(required=False, default=dict)
    location_info = serializers.JSONField(required=False, default=dict)
    
    def get_model_class(self):
        return MongoAnalytics
    
    def validate_event_type(self, value):
        """Validate event type."""
        if not value or not value.strip():
            raise serializers.ValidationError("Event type cannot be empty")
        return value.strip().lower()
    
    def validate_event_name(self, value):
        """Validate event name."""
        if not value or not value.strip():
            raise serializers.ValidationError("Event name cannot be empty")
        return value.strip()


class MongoConfigurationSerializer(BaseMongoSerializer):
    """
    Serializer for MongoConfiguration documents.
    """
    
    key = serializers.CharField(max_length=200, required=True)
    value = serializers.JSONField(required=True)
    data_type = serializers.ChoiceField(
        choices=['string', 'integer', 'float', 'boolean', 'json', 'list'],
        default='string'
    )
    category = serializers.CharField(max_length=100, default='general')
    description = serializers.CharField(required=False, allow_blank=True)
    is_sensitive = serializers.BooleanField(default=False)
    is_active = serializers.BooleanField(default=True)
    environment = serializers.CharField(max_length=50, default='production')
    
    def get_model_class(self):
        return MongoConfiguration
    
    def validate_key(self, value):
        """Validate configuration key."""
        if not value or not value.strip():
            raise serializers.ValidationError("Configuration key cannot be empty")
        return value.strip().upper()
    
    def validate_value(self, value):
        """Validate configuration value."""
        if value is None:
            raise serializers.ValidationError("Configuration value cannot be null")
        return value
    
    def to_representation(self, instance):
        """Convert instance to representation."""
        data = super().to_representation(instance)
        
        # Hide sensitive values in representation
        if hasattr(instance, 'is_sensitive') and instance.is_sensitive:
            data['value'] = '***HIDDEN***'
        
        return data


class ComponentSrcSerializer(BaseMongoSerializer):
    """
    Serializer for ComponentSrc documents with file upload support.
    """
    componentId = serializers.IntegerField(required=True)
    src = serializers.CharField(required=False, allow_blank=True)
    file = serializers.FileField(write_only=True, required=False)

    def get_model_class(self):
        return ComponentSrc

    def validate_componentId(self, value):
        """Validate componentId."""
        if not value:
            raise serializers.ValidationError("ComponentId cannot be empty")
        return value

    def create(self, validated_data):
        """Create ComponentSrc with file upload handling."""
        file_obj = validated_data.pop('file', None)

        if file_obj:
            # Generate file path similar to Django's upload_to
            upload_path = f'component-src/{validated_data["componentId"]}/'
            file_name = file_obj.name

            # Ensure upload directory exists
            full_upload_path = os.path.join(settings.MEDIA_ROOT, upload_path)
            os.makedirs(full_upload_path, exist_ok=True)

            # Save file
            file_path = os.path.join(upload_path, file_name)
            full_file_path = os.path.join(settings.MEDIA_ROOT, file_path)

            with open(full_file_path, 'wb+') as destination:
                for chunk in file_obj.chunks():
                    destination.write(chunk)

            # Store relative path in src field
            validated_data['src'] = file_path

        # Create MongoDB document
        model_class = self.get_model_class()
        instance = model_class(**validated_data)
        return instance

    def update(self, instance, validated_data):
        """Update ComponentSrc with file upload handling."""
        file_obj = validated_data.pop('file', None)

        if file_obj:
            # Delete old file if exists
            if hasattr(instance, 'src') and instance.src:
                old_file_path = os.path.join(settings.MEDIA_ROOT, instance.src)
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)

            # Generate new file path
            upload_path = f'component-src/{instance.componentId}/'
            file_name = file_obj.name

            # Ensure upload directory exists
            full_upload_path = os.path.join(settings.MEDIA_ROOT, upload_path)
            os.makedirs(full_upload_path, exist_ok=True)

            # Save new file
            file_path = os.path.join(upload_path, file_name)
            full_file_path = os.path.join(settings.MEDIA_ROOT, file_path)

            with open(full_file_path, 'wb+') as destination:
                for chunk in file_obj.chunks():
                    destination.write(chunk)

            # Update src field
            validated_data['src'] = file_path

        # Update instance attributes
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        return instance

