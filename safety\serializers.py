from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Safety


class SafetySerializer(BaseModelSerializer):
    class Meta:
        model = Safety
        fields = '__all__'


class SafetyImportSerializer(serializers.Serializer):
    """Serializer for Safety import data validation"""
    systemName = serializers.CharField(max_length=500, required=True)
    safetyFeature = serializers.CharField(max_length=500, required=True)
    safetyStandard = serializers.CharField(max_length=500, required=True)
    riskLevel = serializers.CharField(max_length=1, required=True)
    lastAuditDate = serializers.Char<PERSON>ield(max_length=20, required=False, allow_blank=True)
    complianceStatus = serializers.CharField(max_length=200, required=False, allow_blank=True)
    remark = serializers.CharField(required=False, allow_blank=True)
