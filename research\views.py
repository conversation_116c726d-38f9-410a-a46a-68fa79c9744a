from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from django_filters.rest_framework import DjangoFilterBackend
from .models import Research, ResearchAgency, ResearchResearchAgency, ResearchResearcher, Researcher, ResearchAgencySupportRailwayResearch, AgencySupportRailwayResearch
from .serializers import (
    ResearchSerializer, 
    ResearchDetailSerializer,
    ResearcherSerializer, 
    ResearchAgencySerializer,
    ResearchResearchAgencySerializer,
    ResearchResearcherSerializer,
    ResearchAgencySupportRailwayResearchSerializer
)
import pandas as pd
import io
from django.http import HttpResponse
from rest_framework.decorators import action
from datetime import datetime
import urllib.parse
from django.db import transaction
from utils.util import convert_str_to_bool
from rest_framework.parsers import MultiPartParser
import logging
from django.db.models import Q
from users.models import User

@extend_schema(
    tags=["Research"]
)
class ResearchViewSet(viewsets.ModelViewSet):
    queryset = Research.objects.all()
    serializer_class = ResearchSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['id', 'projectCode', 'projectName', 'status', 'year']
    
    def list(self, request, *args, **kwargs):
        id = request.query_params.get('id')
        projectCode = request.query_params.get('projectCode')
        projectName = request.query_params.get('projectName')
        status = request.query_params.get('status')
        year = request.query_params.get('year')
        agency = request.query_params.get('agency')
        researcher = request.query_params.get('researcher')
        researcher_name = request.query_params.get('name')
        agencySupportRailwayResearch = request.query_params.get('agencySupportRailwayResearch')
        ordering = request.query_params.get('ordering')

        queryset = self.get_queryset()
        if id:
            queryset = queryset.filter(id=id)
        if projectCode:
            queryset = queryset.filter(projectCode__icontains=projectCode)
        if projectName:
            queryset = queryset.filter(projectName__icontains=projectName)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if year:
            queryset = queryset.filter(year=year)
        if agency:
            agency_ids = ResearchAgency.objects.filter(
                name__icontains=agency
            ).values_list('id', flat=True)
            research_ids = ResearchResearchAgency.objects.filter(
                researchAgency__id__in=agency_ids
            ).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
        if researcher or researcher_name:
            if not researcher:
                researcher = researcher_name
            if not researcher_name:
                researcher_name = researcher
            researcher_ids = Researcher.objects.filter(Q(name__icontains=researcher) | Q(name__icontains=researcher_name)).values_list('id', flat=True)
            research_ids = ResearchResearcher.objects.filter(researcher__id__in=researcher_ids).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
        if agencySupportRailwayResearch:
            agency_ids = AgencySupportRailwayResearch.objects.filter(
                name__icontains=agencySupportRailwayResearch
            ).values_list('id', flat=True)
            research_ids = ResearchAgencySupportRailwayResearch.objects.filter(
                agencySupportRailwayResearch__id__in=agency_ids
            ).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
        if ordering:
            queryset = queryset.order_by(ordering)
            
        # Get paginated queryset
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            # Create a custom serialized response
            results = []
            for item in page:
                # Base data from research item
                research_data = ResearchSerializer(item).data
                
                # Get all related agencies
                agency_relations = ResearchResearchAgency.objects.filter(research_id=item.id)
                if agency_relations.exists():
                    research_data['researchAgencies'] = [
                        ResearchAgencySerializer(relation.researchAgency).data 
                        for relation in agency_relations
                    ]
                else:
                    research_data['researchAgencies'] = []
                
                # Get all related researchers
                researcher_relations = ResearchResearcher.objects.filter(research_id=item.id)
                if researcher_relations.exists():
                    research_data['researchers'] = [
                        ResearcherSerializer(relation.researcher).data 
                        for relation in researcher_relations
                    ]
                else:
                    research_data['researchers'] = []
                
                researchAgencySupportRailwayResearch = ResearchAgencySupportRailwayResearch.objects.filter(research_id=item.id)
                if researchAgencySupportRailwayResearch.exists():
                    serialized_data = ResearchAgencySupportRailwayResearchSerializer(researchAgencySupportRailwayResearch, many=True).data
                    research_data['agencySupportRailwayResearch'] = []
                    for relation in serialized_data:
                        if 'agencySupportRailwayResearch' in relation:
                            research_data['agencySupportRailwayResearch'].append(relation['agencySupportRailwayResearch'])
                else:
                    research_data['agencySupportRailwayResearch'] = []
                    
                results.append(research_data)
            
            # Return paginated response with custom data
            return self.get_paginated_response(results)
            
        # If not paginated, use default behavior
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        """
        Create a new Research with related agencies and researchers
        """
        try:
            with transaction.atomic():
                # Create the research first
                research_data = request.data.copy()
                
                # Extract agencies and researchers data
                agencyIds = research_data.pop('researchAgencies', [])
                researcherIds = research_data.pop('researchers', [])
                agencySupportRailwayResearchIds = research_data.pop('agencySupportRailwayResearch', [])
                
                # Create research instance
                research_serializer = ResearchSerializer(data=research_data)
                research_serializer.is_valid(raise_exception=True)
                research = research_serializer.save()
                
                # Create research-agency relationships
                for agencyId in agencyIds:
                    ResearchResearchAgency.objects.create(
                        research=research,
                        researchAgency_id=agencyId
                    )
                
                # Create research-researcher relationships
                for researcherId in researcherIds:
                    ResearchResearcher.objects.create(
                        research=research,
                        researcher_id=researcherId
                    )
                
                # Create research-agencySupportRailwayResearch relationships
                for agencySupportRailwayResearchId in agencySupportRailwayResearchIds:
                    ResearchAgencySupportRailwayResearch.objects.create(
                        research=research,
                        agencySupportRailwayResearch_id=agencySupportRailwayResearchId
                    )
                    
                # Return the complete research data with relationships
                response_data = ResearchSerializer(research).data
                response_data['researchAgencies'] = ResearchResearchAgency.objects.filter(research=research).values_list('researchAgency_id', flat=True)
                response_data['researchers'] = ResearchResearcher.objects.filter(research=research).values_list('researcher_id', flat=True)
                response_data['agencySupportRailwayResearch'] = ResearchAgencySupportRailwayResearch.objects.filter(research=research).values_list('agencySupportRailwayResearch_id', flat=True)

                return Response(response_data, status=201)   
        except Exception as e:
            # Log the error and re-raise it
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating Research: {str(e)}")
            raise

    def update(self, request, *args, **kwargs):
        """
        Update a Research with related agencies and researchers
        """
        try:
            with transaction.atomic():
                # Get the existing research instance
                research = self.get_object()
                
                # Extract agencies and researchers data
                agencyIds = request.data.pop('researchAgencies', [])
                researcherIds = request.data.pop('researchers', [])   
                
                # Update research instance
                research_serializer = ResearchSerializer(research, data=request.data)
                research_serializer.is_valid(raise_exception=True)
                research = research_serializer.save()   
                
                # Delete existing research-agency relationships
                ResearchResearchAgency.objects.filter(research=research).delete()
                
                # Create new research-agency relationships
                for agencyId in agencyIds:
                    ResearchResearchAgency.objects.create(
                        research=research,
                        researchAgency_id=agencyId
                    )
                
                # Delete existing research-researcher relationships
                ResearchResearcher.objects.filter(research=research).delete()
                
                # Create new research-researcher relationships
                for researcherId in researcherIds:
                    ResearchResearcher.objects.create(
                        research=research,
                        researcher_id=researcherId
                    )
                
                # Return the complete research data with relationships
                response_data = ResearchSerializer(research).data
                response_data['researchAgencies'] = ResearchResearchAgency.objects.filter(research=research).values_list('researchAgency_id', flat=True)
                response_data['researchers'] = ResearchResearcher.objects.filter(research=research).values_list('researcher_id', flat=True)
                
                return Response(response_data, status=200)
        except Exception as e:
            # Log the error and re-raise it
            logger = logging.getLogger(__name__)
            logger.error(f"Error updating Research: {str(e)}")
            raise
    
    def destroy(self, request, *args, **kwargs):
        """
        Delete a Research with related agencies and researchers
        """
        try:
            with transaction.atomic():
                # Get the existing research instance
                research = self.get_object()
                
                # Delete related research-agency relationships
                ResearchResearchAgency.objects.filter(research=research).delete()
                
                # Delete related research-researcher relationships
                ResearchResearcher.objects.filter(research=research).delete()
                
                # Delete the research instance
                research.delete()
                
                return Response(status=204)
        except Exception as e:
            # Log the error and re-raise it
            logger = logging.getLogger(__name__)
            logger.error(f"Error deleting Research: {str(e)}")
            raise
    
    @action(detail=False, methods=['post'], url_path='import-excel', permission_classes=[AllowAny], parser_classes=[MultiPartParser])
    def import_excel(self, request, *args, **kwargs):
        """
        Import research data from Excel file
        """
        try:
            excel_file = request.FILES.get('file')
            if not excel_file:
                return Response({'error': 'ไม่พบไฟล์'}, status=status.HTTP_400_BAD_REQUEST)
            
            # For debugging
            logger = logging.getLogger(__name__)
            
            # Try different approaches to read the Excel file
            try:
                # First attempt: Standard reading with skiprows=2
                df = pd.read_excel(excel_file, skiprows=2)
                if df.empty:
                    # If empty, try reading without skipping rows
                    excel_file.seek(0)  # Reset file pointer
                    df = pd.read_excel(excel_file)
            except Exception as excel_error:
                # Log the error
                logger.error(f"Error reading Excel file: {str(excel_error)}")
                # Try reading without skipping rows
                excel_file.seek(0)  # Reset file pointer
                df = pd.read_excel(excel_file)
            
            # Log DataFrame info for debugging
            logger.info(f"DataFrame shape: {df.shape}")
            logger.info(f"DataFrame columns: {df.columns.tolist()}")
            
            if df.empty:
                return Response({
                    'error': 'ไฟล์ Excel ไม่มีข้อมูล',
                    'success_count': 0,
                    'error_count': 0,
                    'error_details': []
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Map column names (support both Thai and English column names)
            column_mappings = {
                'รหัสโครงการ': ['รหัสโครงการ', 'project_code', 'projectcode', 'project code', 'รหัส'],
                'ชื่อโครงการ': ['ชื่อโครงการ', 'project_name', 'projectname', 'project name', 'ชื่อ'],
                'นักวิจัย': ['นักวิจัย', 'researcher', 'researcher_name', 'researchername', 'researcher name'],
                'หน่วยงานวิจัย': ['หน่วยงานวิจัย', 'research_agency', 'researchagency', 'agency', 'research agency'],
                'ปี': ['ปี', 'year', 'project_year'],
                'ลิงก์เว็บไซต์': ['ลิงก์เว็บไซต์', 'website_link', 'websitelink', 'website link', 'ลิงก์เว็บไซต์'],
                'ลิงก์รายงานวิจัยฉบับสมบูรณ์': ['ลิงก์รายงานวิจัยฉบับสมบูรณ์', 'link', 'research_link', 'researchlink', 'research link'],
                'แหล่งทุน': ['แหล่งทุน', 'funding', 'funding_source', 'fundingsource', 'funding source'],
            }
            
            # Map actual column names to expected keys
            column_map = {}
            for expected_key, possible_names in column_mappings.items():
                found = False
                for possible_name in possible_names:
                    if possible_name in df.columns:
                        column_map[expected_key] = possible_name
                        found = True
                        break
                if not found:
                    # For debugging purposes, let's be permissive and not error out
                    logger.warning(f"Column '{expected_key}' not found in Excel file")
            
            # Log the mapped columns
            logger.info(f"Mapped columns: {column_map}")
            
            # Initialize counters for success and error handling
            success_count = 0
            error_count = 0
            error_details = []
            processed_rows = 0
            
            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        processed_rows += 1
                        # Get values from Excel columns using mapped column names
                        project_code = str(row.get(column_map.get('รหัสโครงการ', ''), '')).strip()
                        project_name = str(row.get(column_map.get('ชื่อทุนวิจัย', ''), '')).strip()
                        researcher_name = str(row.get(column_map.get('นักวิจัย', ''), '')).strip()
                        research_agency_names = str(row.get(column_map.get('หน่วยงานวิจัย', ''), '')).strip()
                        website_link = str(row.get(column_map.get('ลิงก์เว็บไซต์', ''), '')).strip()
                        funding_source = str(row.get(column_map.get('แหล่งทุน', ''), '')).strip()
                        

                        # Handle year - could be a number or string
                        year_value = row.get(column_map.get('ปี', ''), '')
                        year = str(year_value).strip() if year_value is not None else ''
                        
                        # Handle link
                        # link_value = row.get(column_map.get('ลิงก์รายงานวิจัยฉบับสมบูรณ์', ''), '')
                        # link = str(link_value).strip() if link_value is not None else ''
                        
                        # Skip rows with empty project_code or project_name
                        if not project_code or not project_name:
                            # Log for debugging
                            logger.warning(f"Row {index+3}: Missing project code or name. Code: '{project_code}', Name: '{project_name}'")
                            error_count += 1
                            error_details.append({
                                'row': index + 3,  # Add 3 to account for header rows
                                'error': 'รหัสโครงการหรือชื่อทุนวิจัยไม่ควรว่าง'
                            })
                            continue
                        
                        # Create or update Research
                        research, created = Research.objects.update_or_create(
                            projectCode=project_code,
                            defaults={
                                'projectName': project_name,
                                'year': year,
                                'status': True,
                                'link': website_link,
                            }
                        )
                        
                        # Handle researcher
                        if researcher_name:
                            researcher_names = [name.strip() for name in researcher_name.split(',')]
                            
                            # Delete existing researcher relationships
                            ResearchResearcher.objects.filter(research=research).delete()
                            
                            # Create new researcher relationships
                            for name in researcher_names:
                                if name:
                                    researcher, _ = Researcher.objects.get_or_create(name=name)
                                    ResearchResearcher.objects.create(
                                        research=research,
                                        researcher=researcher
                                    )
                        
                        # Handle research agencies
                        if research_agency_names:
                            agency_names = [name.strip() for name in research_agency_names.split(',')]
                            
                            # Delete existing agency relationships
                            ResearchResearchAgency.objects.filter(research=research).delete()
                            
                            # Create new agency relationships
                            for name in agency_names:
                                if name:
                                    agency, _ = ResearchAgency.objects.get_or_create(name=name)
                                    ResearchResearchAgency.objects.create(
                                        research=research,
                                        researchAgency=agency
                                    )
                        
                        # Handle funding source
                        if funding_source:
                            funding_sources = [name.strip() for name in funding_source.split(',')]
                            
                            # Delete existing funding source relationships
                            ResearchAgencySupportRailwayResearch.objects.filter(research=research).delete()
                            
                            # Create new funding source relationships
                            for name in funding_sources:
                                if name:
                                    funding_source, _ = AgencySupportRailwayResearch.objects.get_or_create(name=name)
                                    ResearchAgencySupportRailwayResearch.objects.create(
                                        research=research,
                                        agencySupportRailwayResearch=funding_source
                                    )

                        success_count += 1
                        # Log for debugging
                        logger.info(f"Successfully imported research: {project_code} - {project_name}")
                        
                        transaction.commit()
                    except Exception as e:
                        error_count += 1
                        logger.error(f"Error importing row {index+3}: {str(e)}")
                        error_details.append({
                            'row': index + 3,  # Add 3 to account for header rows
                            'error': str(e)
                        })
            
            result = {
                'success_count': success_count,
                'error_count': error_count,
                'error_details': error_details,
                'total_rows_processed': processed_rows,
                'dataframe_shape': df.shape,
                'columns_found': df.columns.tolist(),
                'mapped_columns': column_map
            }
            
            return Response(result, status=status.HTTP_200_OK)     
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Error importing Excel: {str(e)}")
            return Response({
                'error': str(e),
                'success_count': 0,
                'error_count': 0,
                'error_details': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลงานวิจัยและนวัตกรรม</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of research IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        projectCode = request.data.get('projectCode')
        projectName = request.data.get('projectName')
        status = request.data.get('status')
        year = request.data.get('year')
        agency = request.data.get('agency')
        researcher = request.data.get('researcher')
        researcher_name = request.data.get('name')
        agencySupportRailwayResearch = request.data.get('agencySupportRailwayResearch')
              
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if projectCode:
            queryset = queryset.filter(projectCode__icontains=projectCode)
        if projectName:
            queryset = queryset.filter(projectName__icontains=projectName)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if year:
            queryset = queryset.filter(year=year)
        if agency:
            agency_ids = ResearchAgency.objects.filter(
                name__icontains=agency
            ).values_list('id', flat=True)
            research_ids = ResearchResearchAgency.objects.filter(
                researchAgency__id__in=agency_ids
            ).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
        if researcher or researcher_name:
            if not researcher:
                researcher = researcher_name
            if not researcher_name:
                researcher_name = researcher
            researcher_ids = Researcher.objects.filter(Q(name__icontains=researcher) | Q(name__icontains=researcher_name)).values_list('id', flat=True)
            research_ids = ResearchResearcher.objects.filter(researcher__id__in=researcher_ids).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
        if agencySupportRailwayResearch:
            agency_ids = AgencySupportRailwayResearch.objects.filter(
                name__icontains=agencySupportRailwayResearch
            ).values_list('id', flat=True)
            research_ids = ResearchAgencySupportRailwayResearch.objects.filter(
                agencySupportRailwayResearch__id__in=agency_ids
            ).values_list('research_id', flat=True)
            queryset = queryset.filter(id__in=research_ids)
            
        for item in queryset:
            try:
                researchResearchAgency = ResearchResearchAgency.objects.filter(research=item)
                researchAgencyName = []
                for researchAgency in researchResearchAgency:
                    researchAgencyName.append(researchAgency.researchAgency.name)
                item.researchAgencyName = ", ".join(researchAgencyName)
                researcherResearcher = ResearchResearcher.objects.filter(research=item)
                researcherName = []
                for researcher in researcherResearcher:
                    researcherName.append(researcher.researcher.name)
                item.researcherName = ", ".join(researcherName)
                researchAgencySupportRailwayResearch = ResearchAgencySupportRailwayResearch.objects.filter(research=item)
                researchAgencySupportRailwayResearchName = []
                for researchAgencySupportRailwayResearch in researchAgencySupportRailwayResearch:
                    researchAgencySupportRailwayResearchName.append(researchAgencySupportRailwayResearch.agencySupportRailwayResearch.name)
                item.researchAgencySupportRailwayResearchName = ", ".join(researchAgencySupportRailwayResearchName)
                
                item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
                item.createUser = User.objects.get(id=item.createUserId)
            except ResearchResearcher.DoesNotExist:
                item.researcherName = None
            except ResearchResearchAgency.DoesNotExist:
                item.researchAgencyName = None
            except ResearchAgencySupportRailwayResearch.DoesNotExist:
                item.researchAgencySupportRailwayResearchName = None
        

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'รหัสโครงการ': item.projectCode,
                'ชื่อทุนวิจัย': item.projectName,
                'นักวิจัย (หัวหน้าโครงการ)': item.researcherName,
                'หน่วยงานจัดสรรงบประมาณ': item.researchAgencyName,
                'แหล่งทุน': item.researchAgencySupportRailwayResearchName,
                'ปี': item.year,
                'ลิงก์รายงานวิจัยฉบับสมบูรณ์': item.link,
                'บทคัดย่อ': item.abstract,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลงานวิจัยและนวัตกรรม'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:L1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:L2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
    
    
@extend_schema(
    tags=["Research"]
)
class ResearcherViewSet(viewsets.ModelViewSet):
    queryset = Researcher.objects.all()
    serializer_class = ResearcherSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]


@extend_schema(
    tags=["Research"]
)
class ResearchAgencyViewSet(viewsets.ModelViewSet):
    queryset = ResearchAgency.objects.all()
    serializer_class = ResearchAgencySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]


@extend_schema(
    tags=["Research"]
)
class ResearchResearchAgencyViewSet(viewsets.ModelViewSet):
    queryset = ResearchResearchAgency.objects.all()
    serializer_class = ResearchResearchAgencySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]


@extend_schema(
    tags=["Research"]
)
class ResearchResearcherViewSet(viewsets.ModelViewSet):
    queryset = ResearchResearcher.objects.all()
    serializer_class = ResearchResearcherSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['research__projectCode', 'research__projectName', 'research__year', 'researcher__name']
    filterset_fields = ['research__projectCode', 'research__projectName', 'research__year', 'researcher__name']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>List of Research Researcher</h1>
        <p>Parameters:(querystring)</p>
        <ul>
            <li>researchAgency__name: ชื่อหน่วยวิจัย</li>
        </ul>
        """
        research__projectCode = request.query_params.get('research__projectCode')
        research__projectName = request.query_params.get('research__projectName')
        research__year = request.query_params.get('research__year')
        researcher__name = request.query_params.get('researcher__name')
        researchAgency__name = request.query_params.get('researchAgency__name')
        ordering = request.query_params.get('ordering')
        
        queryset = self.get_queryset()
        if research__projectCode:
            queryset = queryset.filter(research__projectCode__icontains=research__projectCode)
        if research__projectName:
            queryset = queryset.filter(research__projectName__icontains=research__projectName)
        if research__year:
            queryset = queryset.filter(research__year=research__year)
        if researcher__name:
            queryset = queryset.filter(researcher__name__icontains=researcher__name)
        if ordering:
            queryset = queryset.order_by(ordering)
        # Filter by research agency name if provided
        if researchAgency__name:
            # Get research IDs that are associated with the specified research agency
            research_ids = ResearchResearchAgency.objects.filter(
                researchAgency__name__icontains=researchAgency__name
            ).values_list('research_id', flat=True)
            
            # Filter the queryset to only include research researchers with matching research IDs
            queryset = queryset.filter(research_id__in=research_ids)
            
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            for item in page:
                try:
                    # Get all research agencies
                    research_agency_relations = ResearchResearchAgency.objects.filter(research__id=item.research.id)
                    if research_agency_relations.exists():
                        # Store list of agency objects
                        item.researchAgencies = [relation.researchAgency for relation in research_agency_relations]
                        # For backward compatibility, store the first one as researchAgency
                        item.researchAgency = research_agency_relations.first().researchAgency
                    else:
                        item.researchAgencies = []
                        item.researchAgency = None
                except Exception as e:
                    # Log the error and continue with None for researchAgency
                    logger = logging.getLogger(__name__)
                    logger.error(f"Error getting research agencies: {str(e)}")
                    item.researchAgencies = []
                    item.researchAgency = None
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        for item in queryset:
            try:
                # Get all research agencies
                research_agency_relations = ResearchResearchAgency.objects.filter(research__id=item.research.id)
                if research_agency_relations.exists():
                    # Store list of agency objects
                    item.researchAgencies = [relation.researchAgency for relation in research_agency_relations]
                    # For backward compatibility, store the first one as researchAgency
                    item.researchAgency = research_agency_relations.first().researchAgency
                else:
                    item.researchAgencies = []
                    item.researchAgency = None
            except Exception as e:
                # Log the error and continue with None for researchAgency
                logger = logging.getLogger(__name__)
                logger.error(f"Error getting research agencies: {str(e)}")
                item.researchAgencies = []
                item.researchAgency = None
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        try:
            # Get all research agencies
            research_agency_relations = ResearchResearchAgency.objects.filter(research__id=instance.research.id)
            if research_agency_relations.exists():
                # Store list of agency objects
                instance.researchAgencies = [relation.researchAgency for relation in research_agency_relations]
                # For backward compatibility, store the first one as researchAgency
                instance.researchAgency = research_agency_relations.first().researchAgency
            else:
                instance.researchAgencies = []
                instance.researchAgency = None
        except Exception as e:
            # Log the error and continue with None for researchAgency
            logger = logging.getLogger(__name__)
            logger.error(f"Error getting research agencies: {str(e)}")
            instance.researchAgencies = []
            instance.researchAgency = None
            
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=False, methods=['post'], url_path='download-excel', permission_classes=[AllowAny])
    def download_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลงานวิจัยและนวัตกรรม</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of research IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')        
        queryset = self.get_queryset()
        if ids:
            researchs = []
            if -1 in ids:
                researchs = Research.objects.exclude(id__in=[id for id in ids if id != -1])
            else:
                researchs = Research.objects.filter(id__in=ids)
            
            if researchs:
                queryset = queryset.filter(research__in=researchs)
        
        # Add research agency information to each item
        for item in queryset:
            try:
                # Get all research agencies
                research_agency_relations = ResearchResearchAgency.objects.filter(research_id=item.research.id)
                if research_agency_relations.exists():
                    # Store list of agency objects
                    item.researchAgencies = [relation.researchAgency for relation in research_agency_relations]
                    # For backward compatibility, store the first one as researchAgency
                    item.researchAgency = research_agency_relations.first().researchAgency
                    
                    # Join agency names for Excel report
                    item.agency_names = ", ".join([agency.name for agency in item.researchAgencies])
                else:
                    item.researchAgencies = []
                    item.researchAgency = None
                    item.agency_names = "N/A"
            except Exception as e:
                # Log the error and continue with None for researchAgency
                logger = logging.getLogger(__name__)
                logger.error(f"Error getting research agencies: {str(e)}")
                item.researchAgencies = []
                item.researchAgency = None
                item.agency_names = "N/A"
        
        # Convert data to DataFrame
        excel_data = []
        for item in queryset:
            excel_data.append({
                'รหัสโครงการ': item.research.projectCode,
                'ชื่อทุนวิจัย': item.research.projectName,
                'นักวิจัย (หัวหน้าโครงการ)': item.researcher.name,
                'หน่วยงานจัดสรรงบประมาณ': item.agency_names,
                'ปี': item.research.year,
                'ลิงก์รายงานวิจัยฉบับสมบูรณ์': item.research.link,
                'บทคัดย่อ': item.research.abstract,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลงานวิจัยและนวัตกรรม'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:G1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:G2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
