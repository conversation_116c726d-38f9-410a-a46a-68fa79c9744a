from django.db import models
from RTRDA.model import BaseModel


class Safety(BaseModel):
    systemName = models.CharField(db_column='SystemName', max_length=500, db_collation='Thai_CI_AI')
    safetyFeature = models.CharField(db_column='SafetyFeature', max_length=500, db_collation='Thai_CI_AI')
    safetyStandard = models.CharField(db_column='SafetyStandard', max_length=500, db_collation='Thai_CI_AI')
    riskLevel = models.CharField(db_column='RiskLevel', max_length=1, db_collation='Thai_CI_AI')
    lastAuditDate = models.DateTimeField(db_column='LastAuditDate', blank=True, null=True)
    complianceStatus = models.CharField(db_column='ComplianceStatus', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Safety'

