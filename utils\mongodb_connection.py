"""
MongoDB Connection Manager for RTRDA Project

This module provides a centralized way to manage MongoDB connections
with proper connection pooling, error handling, and configuration management.
"""

import logging
from typing import Optional, Dict, Any
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo.errors import (
    ConnectionFailure, 
    ServerSelectionTimeoutError, 
    ConfigurationError,
    PyMongoError
)
from django.conf import settings
import threading

logger = logging.getLogger(__name__)


class MongoDBConnectionManager:
    """
    Singleton class to manage MongoDB connections with connection pooling.
    """
    
    _instance = None
    _lock = threading.Lock()
    _client: Optional[MongoClient] = None
    _database: Optional[Database] = None
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MongoDBConnectionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self._connect()
    
    def _build_connection_string(self) -> str:
        """
        Build MongoDB connection string from settings.

        Returns:
            str: MongoDB connection string
        """
        mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})

        host = mongodb_settings.get('host', 'localhost')
        port = mongodb_settings.get('port', 27017)
        username = mongodb_settings.get('username', '')
        password = mongodb_settings.get('password', '')

        if username and password:
            connection_string = f"mongodb://{username}:{password}@{host}:{port}"
        else:
            connection_string = f"mongodb://{host}:{port}"

        return connection_string
    
    def _get_connection_options(self) -> Dict[str, Any]:
        """
        Get MongoDB connection options from settings.

        Returns:
            Dict[str, Any]: Connection options
        """
        mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})

        options = {
            'maxPoolSize': mongodb_settings.get('maxPoolSize', 50),
            'minPoolSize': mongodb_settings.get('minPoolSize', 5),
            'maxIdleTimeMS': mongodb_settings.get('maxIdleTimeMS', 30000),
            'serverSelectionTimeoutMS': mongodb_settings.get('serverSelectionTimeoutMS', 5000),
            'socketTimeoutMS': mongodb_settings.get('socketTimeoutMS', 20000),
            'connectTimeoutMS': mongodb_settings.get('connectTimeoutMS', 20000),
            'retryWrites': True,
            'retryReads': True,
        }

        # Add authentication source if username and password are provided
        username = mongodb_settings.get('username', '')
        password = mongodb_settings.get('password', '')
        auth_source = mongodb_settings.get('authentication_source', 'admin')

        if username and password:
            options['authSource'] = auth_source

        return options
    
    def _connect(self) -> None:
        """
        Establish connection to MongoDB.
        
        Raises:
            ConnectionFailure: If connection to MongoDB fails
            ConfigurationError: If MongoDB configuration is invalid
        """
        try:
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            
            if not mongodb_settings:
                raise ConfigurationError("MONGODB_SETTINGS not found in Django settings")
            
            connection_string = self._build_connection_string()
            connection_options = self._get_connection_options()
            
            logger.info("Connecting to MongoDB...")
            
            self._client = MongoClient(connection_string, **connection_options)
            
            # Test the connection
            self._client.admin.command('ping')
            
            # Get the database
            db_name = mongodb_settings.get('db', 'rtrda_mongodb')
            self._database = self._client[db_name]
            
            logger.info(f"Successfully connected to MongoDB database: {db_name}")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise ConnectionFailure(f"MongoDB connection failed: {e}")
        except ConfigurationError as e:
            logger.error(f"MongoDB configuration error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            raise ConnectionFailure(f"Unexpected MongoDB connection error: {e}")
    
    def get_client(self) -> MongoClient:
        """
        Get MongoDB client instance.
        
        Returns:
            MongoClient: MongoDB client instance
            
        Raises:
            ConnectionFailure: If no active connection exists
        """
        if self._client is None:
            raise ConnectionFailure("No active MongoDB connection")
        return self._client
    
    def get_database(self) -> Database:
        """
        Get MongoDB database instance.
        
        Returns:
            Database: MongoDB database instance
            
        Raises:
            ConnectionFailure: If no active connection exists
        """
        if self._database is None:
            raise ConnectionFailure("No active MongoDB database connection")
        return self._database
    
    def get_collection(self, collection_name: str) -> Collection:
        """
        Get MongoDB collection instance.
        
        Args:
            collection_name (str): Name of the collection
            
        Returns:
            Collection: MongoDB collection instance
            
        Raises:
            ConnectionFailure: If no active connection exists
        """
        database = self.get_database()
        return database[collection_name]
    
    def test_connection(self) -> bool:
        """
        Test MongoDB connection.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            if self._client:
                self._client.admin.command('ping')
                return True
            return False
        except Exception as e:
            logger.error(f"MongoDB connection test failed: {e}")
            return False
    
    def close_connection(self) -> None:
        """
        Close MongoDB connection.
        """
        try:
            if self._client:
                self._client.close()
                logger.info("MongoDB connection closed")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {e}")
        finally:
            self._client = None
            self._database = None
    
    def reconnect(self) -> None:
        """
        Reconnect to MongoDB.
        """
        logger.info("Reconnecting to MongoDB...")
        self.close_connection()
        self._connect()


# Global instance
mongodb_manager = MongoDBConnectionManager()


def get_mongodb_client() -> MongoClient:
    """
    Get MongoDB client instance.
    
    Returns:
        MongoClient: MongoDB client instance
    """
    return mongodb_manager.get_client()


def get_mongodb_database() -> Database:
    """
    Get MongoDB database instance.
    
    Returns:
        Database: MongoDB database instance
    """
    return mongodb_manager.get_database()


def get_mongodb_collection(collection_name: str) -> Collection:
    """
    Get MongoDB collection instance.
    
    Args:
        collection_name (str): Name of the collection
        
    Returns:
        Collection: MongoDB collection instance
    """
    return mongodb_manager.get_collection(collection_name)
