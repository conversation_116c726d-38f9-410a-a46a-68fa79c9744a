from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import MaintenanceWork
from .serializers import MaintenanceWorkSerializer
from datetime import datetime

User = get_user_model()


class MaintenanceWorkSerializerTest(TestCase):
    """Test cases for MaintenanceWorkSerializer"""

    def test_serializer_fields(self):
        """Test that serializer includes all expected fields"""
        serializer = MaintenanceWorkSerializer()
        expected_fields = {
            'id', 'code', 'systemComponent', 'maintenanceType',
            'scheduleDate', 'actualDate', 'technician', 'status',
            'costEstimate', 'remark', 'createUserId', 'createDate',
            'updateUserId', 'updateDate', 'createUser', 'updateUser'
        }
        self.assertEqual(set(serializer.fields.keys()), expected_fields)


class MaintenanceWorkViewSetTest(APITestCase):
    """Test cases for MaintenanceWorkViewSet"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            firstname='Test',
            lastname='User'
        )

    def test_maintenance_work_list_endpoint(self):
        """Test that the maintenance work list endpoint is accessible"""
        response = self.client.get('/api/maintenance-work/')
        # Should return 200 for read-only access
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_maintenance_work_detail_endpoint(self):
        """Test that the maintenance work detail endpoint structure is correct"""
        # This test just checks the endpoint exists and returns proper response structure
        response = self.client.get('/api/maintenance-work/')
        self.assertIn('results', response.data)
        self.assertIn('count', response.data)
