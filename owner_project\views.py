from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from .models import OwnerProject, OwnerProjectMasRailwayLine
from .serializers import OwnerProjectSerializer, OwnerProjectMasRailwayLineSerializer
from utils.pagination import CustomPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from django.db.models import Q
from utils.util import convert_str_to_bool 
from rest_framework.decorators import action
from rest_framework.response import Response
from users.models import User
from django.http import HttpResponse
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from rest_framework.permissions import AllowAny

@extend_schema(
    tags=["Owner Project"]
)
class OwnerProjectViewSet(viewsets.ModelViewSet):
    queryset = OwnerProject.objects.all()
    serializer_class = OwnerProjectSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name','masTrainType__id','status','updateUserId']
    
    def list(self, request, *args, **kwargs):
        '''
        <h3>List Owner Project</h3>
        <p>Parameter:</p>
        <ul>
            <li>name: str</li>
            <li>masTrainType__id: int</li>
            <li>masRailwayLine__id: int</li>
            <li>status: bool</li>
            <li>updateUserId: int</li>
        </ul>
        '''

        self.queryset = self.queryset.all()
        name = request.query_params.get('name')
        masTrainType = request.query_params.get('masTrainType__id')
        status = request.query_params.get('status')
        masRailwayLine = request.query_params.get('masRailwayLine__id')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if name:
            self.queryset = self.queryset.filter(name__icontains=name)
        if masTrainType:
            self.queryset = self.queryset.filter(masTrainType__id=masTrainType)
        if status:
            self.queryset = self.queryset.filter(status=convert_str_to_bool(status))
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if masRailwayLine:
            masRailwayLine = OwnerProjectMasRailwayLine.objects.filter(masRailwayLine__id=masRailwayLine)
            projectIds = masRailwayLine.values_list('ownerProject', flat=True)
            self.queryset = self.queryset.filter(id__in=projectIds)
        if ordering:
            self.queryset = self.queryset.order_by(ordering)
        
        page = self.paginate_queryset(self.queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(self.queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'], url_path='export-excel', permission_classes=[AllowAny])
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลรถไฟชิ้นส่วน และอุปกรณ์</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Component IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        masTrainType = request.data.get('masTrainType__id')
        status = request.data.get('status')
        masRailwayLine = request.data.get('masRailwayLine__id')
        userId = request.data.get('userId')
        
        queryset = self.get_queryset()
        
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if masTrainType:
            queryset = queryset.filter(masTrainType__id=masTrainType)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if masRailwayLine:
            masRailwayLine = OwnerProjectMasRailwayLine.objects.filter(masRailwayLine__id=masRailwayLine)
            projectIds = masRailwayLine.values_list('ownerProject', flat=True)
            queryset = queryset.filter(id__in=projectIds)
           
        for item in queryset:
            item.ownerProjectMasRailwayLine = OwnerProjectMasRailwayLine.objects.filter(ownerProject=item)
            item.masRailwayLine = ''
            for i, ownerProjectMasRailwayLine in enumerate(item.ownerProjectMasRailwayLine):
                if i == len(item.ownerProjectMasRailwayLine) - 1:
                    item.masRailwayLine += ownerProjectMasRailwayLine.masRailwayLine.name
                else:
                    item.masRailwayLine += ownerProjectMasRailwayLine.masRailwayLine.name + ', '
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อเจ้าของโครงการ': item.name,
                'ประเภท': item.masTrainType.name if item.masTrainType else '',
                'สายรถไฟ': item.masRailwayLine,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลเจ้าของโครงการระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:F1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:F2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["Owner Project"]
)
class OwnerProjectMasRailwayLineViewSet(viewsets.ModelViewSet):
    queryset = OwnerProjectMasRailwayLine.objects.all()
    serializer_class = OwnerProjectMasRailwayLineSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['ownerProject__id', 'masRailwayLine__id']
