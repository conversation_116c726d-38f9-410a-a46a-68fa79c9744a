from django.db import models
from RTRDA.model import BaseModel
from mas.models import MasTrainType, MasRailwayLine


class Route(BaseModel):
    masTrainType = models.ForeignKey(MasTrainType, on_delete=models.PROTECT, db_column='MasTrainTypeId')
    masRailwayLine = models.ForeignKey(MasRailwayLine, on_delete=models.PROTECT, db_column='MasRailwayLineId')
    name = models.CharField(db_column='Name', max_length=200, db_collation='Thai_CI_AI')
    origin = models.CharField(db_column='Origin', max_length=500, db_collation='Thai_CI_AI')
    destination = models.CharField(db_column='Destination', max_length=500, db_collation='Thai_CI_AI')
    status = models.BooleanField(db_column='Status', default=True)

    class Meta:
        managed = False
        db_table = 'Route'


class Servicefares(BaseModel):
    route = models.ForeignKey(Route, on_delete=models.PROTECT, db_column='RouteId')
    originStation = models.CharField(db_column='OriginStation', max_length=500, db_collation='Thai_CI_AI')
    destinationStation = models.CharField(db_column='DestinationStation', max_length=500, db_collation='Thai_CI_AI')
    fareType = models.CharField(db_column='FareType', max_length=1, db_collation='Thai_CI_AI')
    fareAmount = models.FloatField(db_column='FareAmount')
    serviceType = models.CharField(db_column='ServiceType', max_length=1, db_collation='Thai_CI_AI')
    frequency = models.CharField(db_column='Frequency', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    operationStart = models.CharField(db_column='OperationStart', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)
    operationEnd = models.CharField(db_column='OperationEnd', max_length=10, db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField(db_column='Status', default=True)

    class Meta:
        managed = False
        db_table = 'ServiceFares'
        
