"""
MongoDB CRUD Operations for RTRDA Project

This module provides comprehensive CRUD (Create, Read, Update, Delete) operations
for MongoDB collections with proper error handling and logging.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from bson import ObjectId
from bson.errors import InvalidId
from pymongo.errors import (
    DuplicateKeyError,
    WriteError,
    BulkWriteError,
    PyMongoError
)
from pymongo.collection import Collection
from pymongo.results import Insert<PERSON>neResult, InsertManyResult, UpdateResult, DeleteResult
from .mongodb_connection import get_mongodb_collection

logger = logging.getLogger(__name__)


class MongoDBCRUD:
    """
    Comprehensive CRUD operations class for MongoDB collections.
    """
    
    def __init__(self, collection_name: str):
        """
        Initialize CRUD operations for a specific collection.
        
        Args:
            collection_name (str): Name of the MongoDB collection
        """
        self.collection_name = collection_name
        self.collection: Collection = get_mongodb_collection(collection_name)
    
    def _add_timestamps(self, document: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """
        Add timestamp fields to document.
        
        Args:
            document (Dict[str, Any]): Document to add timestamps to
            is_update (bool): Whether this is an update operation
            
        Returns:
            Dict[str, Any]: Document with timestamps
        """
        now = datetime.utcnow()
        
        if not is_update:
            document['created_at'] = now
        
        document['updated_at'] = now
        return document
    
    def _validate_object_id(self, id_value: Union[str, ObjectId]) -> ObjectId:
        """
        Validate and convert string ID to ObjectId.
        
        Args:
            id_value (Union[str, ObjectId]): ID value to validate
            
        Returns:
            ObjectId: Valid ObjectId
            
        Raises:
            ValueError: If ID is invalid
        """
        try:
            if isinstance(id_value, str):
                return ObjectId(id_value)
            elif isinstance(id_value, ObjectId):
                return id_value
            else:
                raise ValueError(f"Invalid ID type: {type(id_value)}")
        except InvalidId as e:
            raise ValueError(f"Invalid ObjectId: {e}")
    
    def create_one(self, document: Dict[str, Any]) -> InsertOneResult:
        """
        Create a single document in the collection.
        
        Args:
            document (Dict[str, Any]): Document to insert
            
        Returns:
            InsertOneResult: Result of the insert operation
            
        Raises:
            DuplicateKeyError: If document violates unique constraints
            WriteError: If write operation fails
        """
        try:
            document = self._add_timestamps(document.copy())
            result = self.collection.insert_one(document)
            logger.info(f"Created document in {self.collection_name}: {result.inserted_id}")
            return result
        except DuplicateKeyError as e:
            logger.error(f"Duplicate key error in {self.collection_name}: {e}")
            raise
        except WriteError as e:
            logger.error(f"Write error in {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating document in {self.collection_name}: {e}")
            raise
    
    def create_many(self, documents: List[Dict[str, Any]]) -> InsertManyResult:
        """
        Create multiple documents in the collection.
        
        Args:
            documents (List[Dict[str, Any]]): List of documents to insert
            
        Returns:
            InsertManyResult: Result of the insert operation
            
        Raises:
            BulkWriteError: If bulk write operation fails
        """
        try:
            documents_with_timestamps = [
                self._add_timestamps(doc.copy()) for doc in documents
            ]
            result = self.collection.insert_many(documents_with_timestamps)
            logger.info(f"Created {len(result.inserted_ids)} documents in {self.collection_name}")
            return result
        except BulkWriteError as e:
            logger.error(f"Bulk write error in {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating documents in {self.collection_name}: {e}")
            raise
    
    def find_one(self, filter_dict: Dict[str, Any] = None, projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Find a single document in the collection.
        
        Args:
            filter_dict (Dict[str, Any], optional): Filter criteria
            projection (Dict[str, Any], optional): Fields to include/exclude
            
        Returns:
            Optional[Dict[str, Any]]: Found document or None
        """
        try:
            filter_dict = filter_dict or {}
            result = self.collection.find_one(filter_dict, projection)
            if result:
                # Convert ObjectId to string for JSON serialization
                result['_id'] = str(result['_id'])
            return result
        except Exception as e:
            logger.error(f"Error finding document in {self.collection_name}: {e}")
            raise
    
    def find_by_id(self, document_id: Union[str, ObjectId], projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Find a document by its ID.
        
        Args:
            document_id (Union[str, ObjectId]): Document ID
            projection (Dict[str, Any], optional): Fields to include/exclude
            
        Returns:
            Optional[Dict[str, Any]]: Found document or None
            
        Raises:
            ValueError: If ID is invalid
        """
        try:
            object_id = self._validate_object_id(document_id)
            return self.find_one({'_id': object_id}, projection)
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error finding document by ID in {self.collection_name}: {e}")
            raise
    
    def find_many(self, 
                  filter_dict: Dict[str, Any] = None, 
                  projection: Dict[str, Any] = None,
                  sort: List[tuple] = None,
                  limit: int = None,
                  skip: int = None) -> List[Dict[str, Any]]:
        """
        Find multiple documents in the collection.
        
        Args:
            filter_dict (Dict[str, Any], optional): Filter criteria
            projection (Dict[str, Any], optional): Fields to include/exclude
            sort (List[tuple], optional): Sort criteria
            limit (int, optional): Maximum number of documents to return
            skip (int, optional): Number of documents to skip
            
        Returns:
            List[Dict[str, Any]]: List of found documents
        """
        try:
            filter_dict = filter_dict or {}
            cursor = self.collection.find(filter_dict, projection)
            
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            
            results = list(cursor)
            # Convert ObjectId to string for JSON serialization
            for result in results:
                result['_id'] = str(result['_id'])
            
            return results
        except Exception as e:
            logger.error(f"Error finding documents in {self.collection_name}: {e}")
            raise
    
    def count_documents(self, filter_dict: Dict[str, Any] = None) -> int:
        """
        Count documents in the collection.

        Args:
            filter_dict (Dict[str, Any], optional): Filter criteria

        Returns:
            int: Number of documents matching the filter
        """
        try:
            filter_dict = filter_dict or {}
            return self.collection.count_documents(filter_dict)
        except Exception as e:
            logger.error(f"Error counting documents in {self.collection_name}: {e}")
            raise

    def update_one(self,
                   filter_dict: Dict[str, Any],
                   update_dict: Dict[str, Any],
                   upsert: bool = False) -> UpdateResult:
        """
        Update a single document in the collection.

        Args:
            filter_dict (Dict[str, Any]): Filter criteria to find document
            update_dict (Dict[str, Any]): Update operations
            upsert (bool): Whether to insert if document doesn't exist

        Returns:
            UpdateResult: Result of the update operation

        Raises:
            WriteError: If update operation fails
        """
        try:
            # Add timestamp for update
            if '$set' not in update_dict:
                update_dict['$set'] = {}
            update_dict['$set']['updated_at'] = datetime.utcnow()

            result = self.collection.update_one(filter_dict, update_dict, upsert=upsert)
            logger.info(f"Updated document in {self.collection_name}: matched={result.matched_count}, modified={result.modified_count}")
            return result
        except WriteError as e:
            logger.error(f"Write error updating document in {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error updating document in {self.collection_name}: {e}")
            raise

    def update_by_id(self,
                     document_id: Union[str, ObjectId],
                     update_dict: Dict[str, Any],
                     upsert: bool = False) -> UpdateResult:
        """
        Update a document by its ID.

        Args:
            document_id (Union[str, ObjectId]): Document ID
            update_dict (Dict[str, Any]): Update operations
            upsert (bool): Whether to insert if document doesn't exist

        Returns:
            UpdateResult: Result of the update operation

        Raises:
            ValueError: If ID is invalid
            WriteError: If update operation fails
        """
        try:
            object_id = self._validate_object_id(document_id)
            return self.update_one({'_id': object_id}, update_dict, upsert)
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error updating document by ID in {self.collection_name}: {e}")
            raise

    def update_many(self,
                    filter_dict: Dict[str, Any],
                    update_dict: Dict[str, Any],
                    upsert: bool = False) -> UpdateResult:
        """
        Update multiple documents in the collection.

        Args:
            filter_dict (Dict[str, Any]): Filter criteria to find documents
            update_dict (Dict[str, Any]): Update operations
            upsert (bool): Whether to insert if no documents match

        Returns:
            UpdateResult: Result of the update operation

        Raises:
            WriteError: If update operation fails
        """
        try:
            # Add timestamp for update
            if '$set' not in update_dict:
                update_dict['$set'] = {}
            update_dict['$set']['updated_at'] = datetime.utcnow()

            result = self.collection.update_many(filter_dict, update_dict, upsert=upsert)
            logger.info(f"Updated documents in {self.collection_name}: matched={result.matched_count}, modified={result.modified_count}")
            return result
        except WriteError as e:
            logger.error(f"Write error updating documents in {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error updating documents in {self.collection_name}: {e}")
            raise

    def delete_one(self, filter_dict: Dict[str, Any]) -> DeleteResult:
        """
        Delete a single document from the collection.

        Args:
            filter_dict (Dict[str, Any]): Filter criteria to find document

        Returns:
            DeleteResult: Result of the delete operation

        Raises:
            WriteError: If delete operation fails
        """
        try:
            result = self.collection.delete_one(filter_dict)
            logger.info(f"Deleted document from {self.collection_name}: deleted_count={result.deleted_count}")
            return result
        except WriteError as e:
            logger.error(f"Write error deleting document from {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error deleting document from {self.collection_name}: {e}")
            raise

    def delete_by_id(self, document_id: Union[str, ObjectId]) -> DeleteResult:
        """
        Delete a document by its ID.

        Args:
            document_id (Union[str, ObjectId]): Document ID

        Returns:
            DeleteResult: Result of the delete operation

        Raises:
            ValueError: If ID is invalid
            WriteError: If delete operation fails
        """
        try:
            object_id = self._validate_object_id(document_id)
            return self.delete_one({'_id': object_id})
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"Error deleting document by ID from {self.collection_name}: {e}")
            raise

    def delete_many(self, filter_dict: Dict[str, Any]) -> DeleteResult:
        """
        Delete multiple documents from the collection.

        Args:
            filter_dict (Dict[str, Any]): Filter criteria to find documents

        Returns:
            DeleteResult: Result of the delete operation

        Raises:
            WriteError: If delete operation fails
        """
        try:
            result = self.collection.delete_many(filter_dict)
            logger.info(f"Deleted documents from {self.collection_name}: deleted_count={result.deleted_count}")
            return result
        except WriteError as e:
            logger.error(f"Write error deleting documents from {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error deleting documents from {self.collection_name}: {e}")
            raise

    def replace_one(self,
                    filter_dict: Dict[str, Any],
                    replacement: Dict[str, Any],
                    upsert: bool = False) -> UpdateResult:
        """
        Replace a single document in the collection.

        Args:
            filter_dict (Dict[str, Any]): Filter criteria to find document
            replacement (Dict[str, Any]): Replacement document
            upsert (bool): Whether to insert if document doesn't exist

        Returns:
            UpdateResult: Result of the replace operation

        Raises:
            WriteError: If replace operation fails
        """
        try:
            replacement = self._add_timestamps(replacement.copy(), is_update=True)
            result = self.collection.replace_one(filter_dict, replacement, upsert=upsert)
            logger.info(f"Replaced document in {self.collection_name}: matched={result.matched_count}, modified={result.modified_count}")
            return result
        except WriteError as e:
            logger.error(f"Write error replacing document in {self.collection_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error replacing document in {self.collection_name}: {e}")
            raise

    def aggregate(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Perform aggregation on the collection.

        Args:
            pipeline (List[Dict[str, Any]]): Aggregation pipeline

        Returns:
            List[Dict[str, Any]]: Aggregation results
        """
        try:
            results = list(self.collection.aggregate(pipeline))
            # Convert ObjectId to string for JSON serialization
            for result in results:
                if '_id' in result and isinstance(result['_id'], ObjectId):
                    result['_id'] = str(result['_id'])
            return results
        except Exception as e:
            logger.error(f"Error performing aggregation on {self.collection_name}: {e}")
            raise

    def create_index(self, keys: Union[str, List[tuple]], **kwargs) -> str:
        """
        Create an index on the collection.

        Args:
            keys (Union[str, List[tuple]]): Index specification
            **kwargs: Additional index options

        Returns:
            str: Name of the created index
        """
        try:
            result = self.collection.create_index(keys, **kwargs)
            logger.info(f"Created index on {self.collection_name}: {result}")
            return result
        except Exception as e:
            logger.error(f"Error creating index on {self.collection_name}: {e}")
            raise

    def drop_index(self, index_name: str) -> None:
        """
        Drop an index from the collection.

        Args:
            index_name (str): Name of the index to drop
        """
        try:
            self.collection.drop_index(index_name)
            logger.info(f"Dropped index from {self.collection_name}: {index_name}")
        except Exception as e:
            logger.error(f"Error dropping index from {self.collection_name}: {e}")
            raise
