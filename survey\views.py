from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from .models import Survey, SurveyChartData, MasAgeRange, MasUserType
from .serializers import SurveySerializer, SurveyChartDataSerializer, MasAgeRangeSerializer, MasUserTypeSerializer
from utils.pagination import CustomPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.decorators import action
from django.db.models import Count
from rest_framework.response import Response
from django.http import HttpResponse
import io
import pandas as pd
import urllib.parse
from datetime import datetime
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time


class SurveyViewSet(viewsets.ModelViewSet):
    queryset = Survey.objects.all()
    serializer_class = SurveySerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        if startDate and endDate:
            queryset = queryset.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
            
        page = self.paginate_queryset(queryset)
        if page is not None:
            return self.get_paginated_response(self.serializer_class(page, many=True).data)
        return super().list(request, *args, **kwargs)
    
    @action(detail=False, methods=['GET'], url_path='chart-data')
    def get_chart_data(self, request):
        """
        <h1>ข้อมูลกราฟ</h1>
        <p>Parameter</p>
        <ul>
            <li>type: (Required)</li>
            <ul>
                <li>ageRange</li>
                <li>userType</li>
                <li>objective</li>
                <li>access</li>
                <li>update</li>
                <li>support</li>
                <li>channel</li>
            </ul>
            <li>startDate: (Required)</li>
            <li>endDate: (Required)</li>
        </ul>
        """
        surveyChartData = SurveyChartData()
        labels = []
        datas = []
        type = request.query_params.get('type')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        
        if type:
            if type == 'ageRange':
                ageRange = MasAgeRange.objects.all()
                masAgeRangeSerializer = MasAgeRangeSerializer(ageRange, many=True)
                for ageRange in masAgeRangeSerializer.data:
                    labels.append(ageRange['name'])
                    datas.append(Survey.objects.filter(masAgeRange=ageRange['id'], createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate))).count())
                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'userType':
                userType = MasUserType.objects.all()
                masUserTypeSerializer = MasUserTypeSerializer(userType, many=True)
                for userType in masUserTypeSerializer.data:
                    labels.append(userType['name'])
                    datas.append(Survey.objects.filter(masUserType=userType['id'], createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate))).count())
                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'objective':
                labels = [
                    'ข้อมูลทางสถิติ',
                    'ข้อมูลเส้นทางรถไฟฟ้า/รถไฟไทย',
                    'ข้อมูลโครงการที่กำลังก่อสร้าง',
                    'ข้อมูลความปลอดภัย',
                    'ข้อมูลโรงงานอุตสาหกรรม',
                    'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง',
                    'ข้อมูลมาตรฐานระบบราง',
                    'ข้อมูลศูนย์ทดสอบ',
                    'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย',
                    'ข้อมูลการซ่อมบำรุง',
                    'ข้อมูลสิ่งแวดล้อม',
                    'ข้อมูลงานวิจัยและนวัตกรรม',
                    'ข้อมูลหลักสูตร',
                    'ข้อมูลการอบรม',
                    'ข้อมูลข่าวสารและกิจกรรม',
                    'อื่นๆ (โปรดระบุ)'
                ]
                objective = Survey.objects.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
                objectiveSerializer = SurveySerializer(objective, many=True)
                datas = [0] * 16
                for objective in objectiveSerializer.data:
                    if objective['isObjectiveStatistic']:
                        datas[0] += 1
                    if objective['isObjectiveRoute']:
                        datas[1] += 1
                    if objective['isObjectiveUnderConstruction']:
                        datas[2] += 1
                    if objective['isObjectiveSafety']:
                        datas[3] += 1
                    if objective['isObjectiveIndustrial']:
                        datas[4] += 1
                    if objective['isObjectiveEquipment']:
                        datas[5] += 1
                    if objective['isObjectiveStandard']:
                        datas[6] += 1
                    if objective['isObjectiveTestingCenter']:
                        datas[7] += 1
                    if objective['isObjectiveRailwayIndustry']:
                        datas[8] += 1
                    if objective['isObjectiveMaintenance']:
                        datas[9] += 1
                    if objective['isObjectiveEnvironment']:
                        datas[10] += 1
                    if objective['isObjectiveResearch']:
                        datas[11] += 1
                    if objective['isObjectiveCourse']:
                        datas[12] += 1
                    if objective['isObjectiveTraning']:
                        datas[13] += 1
                    if objective['isObjectiveNews']:
                        datas[14] += 1
                    if objective['isObjectiveOther']:
                        datas[15] += 1
                        
                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'access':
                labels = [
                    'แดชบอร์ด (Dashboard)',
                    'แผนที่เชิงโต้ตอบ (Interactive Map)',
                    'ไฟล์ข้อมูลดิจิทัล (CSV, Excel, JSON)',
                    'เอกสาร รายงานหรือบทวิเคราะห์',
                    'อื่นๆ (โปรดระบุ)'
                ]
                
                access = Survey.objects.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
                accessSerializer = SurveySerializer(access, many=True)
                datas = [0] * 5
                for access in accessSerializer.data:
                    if access['isAccessDashboard']:
                        datas[0] += 1
                    if access['isAccessMap']:
                        datas[1] += 1
                    if access['isAccessDigitalData']:
                        datas[2] += 1
                    if access['isAccessDocument']:
                        datas[3] += 1
                    if access['isAccessOther']:
                        datas[4] += 1   

                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'update':
                labels = [
                    'แบบเรียลไทม์',
                    'รายวัน',
                    'รายเดือน',
                    'รายปี',
                ]
                update = Survey.objects.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
                updateSerializer = SurveySerializer(update, many=True)
                datas = [0] * 4
                for update in updateSerializer.data:
                    if update['isUpdateRealTime']:
                        datas[0] += 1
                    if update['isUpdateDaily']:
                        datas[1] += 1
                    if update['isUpdateMonthly']:
                        datas[2] += 1
                    if update['isUpdateYearly']:
                        datas[3] += 1

                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'support':
                labels = [
                    'การติดตามข้อมูลข่าวสาร',
                    'การวิเคราะห์และพัฒนาธุรกิจ',
                    'การกําหนดนโยบายและกํากับดูแล',
                    'การวิจัยและพัฒนานวัตกรรม',
                    'อื่นๆ (โปรดระบุ)',
                ]
                support = Survey.objects.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
                supportSerializer = SurveySerializer(support, many=True)
                datas = [0] * 5
                for support in supportSerializer.data:
                    if support['isSupportInformationTracking']:
                        datas[0] += 1
                    if support['isSupportBusinessAnalysis']:
                        datas[1] += 1
                    if support['isSupportPolicyFormulation']:
                        datas[2] += 1
                    if support['isSupportResearch']:
                        datas[3] += 1
                    if support['isSupportOther']:
                        datas[4] += 1
                        
                surveyChartData.label = labels
                surveyChartData.data = datas
            elif type == 'channel':
                labels = [
                    'เว็บไซต์',
                    'แอปพลิเคชันบนสมาร์ทโฟน',
                    'เชื่อมโยง API (Application Program Interface)',
                    'ติดต่อเจ้าหน้าที่',
                    'อื่นๆ (โปรดระบุ)',
                ]
                channel = Survey.objects.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
                channelSerializer = SurveySerializer(channel, many=True)
                datas = [0] * 5
                for channel in channelSerializer.data:
                    if channel['isChannelWebsite']:
                        datas[0] += 1
                    if channel['isChannelApplication']:
                        datas[1] += 1
                    if channel['isChannelAPI']:
                        datas[2] += 1
                    if channel['isChannelContact']:
                        datas[3] += 1
                    if channel['isChannelOther']:
                        datas[4] += 1

                surveyChartData.label = labels
                surveyChartData.data = datas
            else:
                return Response({'error': 'type ' + type + ' is not supported'})
        else:
            return Response({'error': 'type is required'})
        return Response(SurveyChartDataSerializer(surveyChartData).data)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลแบบสำรวจ</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of survey IDs to include in the export
            "startDate": "01/01/2025",
            "endDate": "31/01/2025"
        }
        </pre>
        """
        
        ids = request.data.get('ids')        
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if startDate and endDate:
            queryset = queryset.filter(createDate__range=(convert_str_to_date_min_time(startDate), convert_str_to_date_max_time(endDate)))
            
        for item in queryset:
            try:
                masAgeRange = MasAgeRange.objects.get(id=item.masAgeRange.id)
                item.ageRangeName = masAgeRange.name
                masUserType = MasUserType.objects.get(id=item.masUserType.id)
                item.userTypeName = masUserType.name
            except MasAgeRange.DoesNotExist:
                item.ageRangeName = None
            except MasUserType.DoesNotExist:
                item.userTypeName = None

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            objective = [
                'ข้อมูลทางสถิติ',
                'ข้อมูลเส้นทางรถไฟฟ้า/รถไฟไทย',
                'ข้อมูลโครงการที่กำลังก่อสร้าง',
                'ข้อมูลความปลอดภัย',
                'ข้อมูลโรงงานอุตสาหกรรม',
                'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง',
                'ข้อมูลมาตรฐานระบบราง',
                'ข้อมูลศูนย์ทดสอบ',
                'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย',
                'ข้อมูลการซ่อมบำรุง',
                'ข้อมูลสิ่งแวดล้อม',
                'ข้อมูลงานวิจัยและนวัตกรรม',
                'ข้อมูลหลักสูตร',
                'ข้อมูลการอบรม',
                'ข้อมูลข่าวสารและกิจกรรม',
                'อื่นๆ (โปรดระบุ)',
            ]
            access = [
                'แดชบอร์ด (Dashboard)',
                'แผนที่เชิงโต้ตอบ (Interactive Map)',
                'ไฟล์ข้อมูลดิจิทัล (CSV, Excel, JSON)',
                'เอกสาร รายงานหรือบทวิเคราะห์',
                'อื่นๆ (โปรดระบุ)'
            ]
            update = [
                'แบบเรียลไทม์',
                'รายวัน',
                'รายเดือน',
                'รายปี',
            ]
            support = [
                'การติดตามข้อมูลข่าวสาร',
                'การวิเคราะห์และพัฒนาธุรกิจ',
                'การกําหนดนโยบายและกํากับดูแล',
                'การวิจัยและพัฒนานวัตกรรม',
                'อื่นๆ (โปรดระบุ)',
            ]
            channel = [
                'เว็บไซต์',
                'แอปพลิเคชันบนสมาร์ทโฟน',
                'เชื่อมโยง API (Application Program Interface)',
                'ติดต่อเจ้าหน้าที่',
            ]
            objective_remove = []
            access_remove = []
            update_remove = []
            support_remove = []
            channel_remove = []
            if not item.isObjectiveStatistic :
                objective_remove.append(0)
            if not item.isObjectiveRoute :
                objective_remove.append(1)
            if not item.isObjectiveUnderConstruction :
                objective_remove.append(2)
            if not item.isObjectiveSafety :
                objective_remove.append(3)
            if not item.isObjectiveIndustrial :
                objective_remove.append(4)
            if not item.isObjectiveEquipment :
                objective_remove.append(5)
            if not item.isObjectiveStandard :
                objective_remove.append(6)
            if not item.isObjectiveTestingCenter :
                objective_remove.append(7)
            if not item.isObjectiveRailwayIndustry :
                objective_remove.append(8)
            if not item.isObjectiveMaintenance :
                objective_remove.append(9)
            if not item.isObjectiveEnvironment :
                objective_remove.append(10)
            if item.isObjectiveResearch :
                objective_remove.append(11)
            if item.isObjectiveCourse :
                objective_remove.append(12)
            if not item.isObjectiveTraning :
                objective_remove.append(13)
            if not item.isObjectiveNews :
                objective_remove.append(14)
            if item.isObjectiveOther :
                objective.append(f'อื่นๆ (โปรดระบุ) : {item.objectiveOther}')
            objective = [objective[i] for i in range(len(objective)) if i not in objective_remove]
                
            if not item.isAccessDashboard :
                access_remove.append(0)
            if not item.isAccessMap :
                access_remove.append(1)
            if not item.isAccessDigitalData :
                access_remove.append(2)
            if not item.isAccessDocument :
                access_remove.append(3)
            if item.isAccessOther :
                access.append(f'อื่นๆ (โปรดระบุ) : {item.accessOther}')
            access = [access[i] for i in range(len(access)) if i not in access_remove]
            
            if not item.isUpdateRealTime :
                update_remove.append(0)
            if not item.isUpdateDaily :
                update_remove.append(1)
            if not item.isUpdateMonthly :
                update_remove.append(2)
            if not item.isUpdateYearly :
                update_remove.append(3)
            update = [update[i] for i in range(len(update)) if i not in update_remove]

            if item.isSupportInformationTracking :
                support_remove.append(0)
            if item.isSupportBusinessAnalysis :
                support_remove.append(1)
            if item.isSupportPolicyFormulation :
                support_remove.append(2)
            if item.isSupportResearch :
                support_remove.append(3)
            if item.isSupportOther :
                support.append(f'อื่นๆ (โปรดระบุ) : {item.supportOther}')
            support = [support[i] for i in range(len(support)) if i not in support_remove]
                
            if not item.isChannelWebsite :
                channel_remove.append(0)
            if not item.isChannelApplication :
                channel_remove.append(1)
            if not item.isChannelAPI :
                channel_remove.append(2)
            if not item.isChannelContact :
                channel_remove.append(3)
            if item.isChannelOther :
                channel.append(f'อื่นๆ (โปรดระบุ) : {item.channelOther}')
            channel = [channel[i] for i in range(len(channel)) if i not in channel_remove]

            excel_data.append({
                'ลำดับ': count,
                'ชื่อ-นามสกุล': item.name,
                'ช่วงอายุ': item.ageRangeName,
                'ประเภทผู้ใช้งาน': item.userTypeName,
                'ชื่อหน่วยงาน/บริษัท/สถาบัน': item.departmentName,
                'อีเมล': item.email,
                'วัตถุประสงค์หลักของการใช้ข้อมูลระบบราง': ', '.join(objective),
                'ท่านต้องการเข้าถึงข้อมูลในรูปแบบใด': ', '.join(access),
                'ความถี่ในการอัปเดตข้อมูลที่ท่านต้องการ': ', '.join(update),
                'ท่านต้องการให้ฐานข้อมูลนี้สนับสนุนการทำงานของท่านในด้านใด': ', '.join(support),
                'ช่องทางการเข้าถึงฐานข้อมูลที่ท่านสะดวกใช้งาน': ', '.join(channel),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรายงานแบบสำรวจ'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=3)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:K1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Add date range header
            # Parse dates flexibly to handle different formats
            try:
                # Try different date formats
                date_formats = ['%Y-%m-%d', '%d-%m-%Y', '%d/%m/%Y']
                start_date_obj = None
                end_date_obj = None
                
                for fmt in date_formats:
                    try:
                        start_date_obj = datetime.strptime(startDate, fmt)
                        break
                    except ValueError:
                        continue
                
                for fmt in date_formats:
                    try:
                        end_date_obj = datetime.strptime(endDate, fmt)
                        break
                    except ValueError:
                        continue
                
                # If we couldn't parse the dates, use safer defaults
                if start_date_obj is None:
                    # Try to extract dates from the original filtering
                    start_date_obj = convert_str_to_date_min_time(startDate)
                
                if end_date_obj is None:
                    end_date_obj = convert_str_to_date_max_time(endDate)
                
                # Convert to Thai Buddhist Era year
                start_thai_year = start_date_obj.year + 543
                end_thai_year = end_date_obj.year + 543
                
                # Format dates in Thai style
                start_thai_date = f"{start_date_obj.day} {thai_months[start_date_obj.month]} {start_thai_year}"
                end_thai_date = f"{end_date_obj.day} {thai_months[end_date_obj.month]} {end_thai_year}"
                
                date_range = f"ช่วงวันที่ : {start_thai_date} - {end_thai_date}"
            except Exception as e:
                # Fallback to raw strings if there's any error in parsing
                date_range = f"ช่วงวันที่ : {startDate} - {endDate}"
            
            date_range_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:K2', date_range, date_range_format)
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A3:K3', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
