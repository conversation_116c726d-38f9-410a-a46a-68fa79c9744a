from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from .models import Training, TrainingCategory
from .serializers import TrainingSerializer, TrainingCategorySerializer
from utils.pagination import CustomPagination
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ars<PERSON>, JSONParser
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from utils.util import convert_str_to_bool, convert_str_to_date_min_time, convert_str_to_date_max_time
from rest_framework.decorators import action
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from django.http import HttpResponse
from users.models import User

@extend_schema(
    tags=["Training"]
)
class TrainingCategoryViewSet(viewsets.ModelViewSet):
    queryset = TrainingCategory.objects.all()
    serializer_class = TrainingCategorySerializer
    pagination_class = CustomPagination
    permission_classes = [Is<PERSON><PERSON>enticatedOr<PERSON>eadOnly]
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Form<PERSON>arser)
    filterset_fields = ['status']


@extend_schema(
    tags=["Training"]
)
class TrainingViewSet(viewsets.ModelViewSet):
    queryset = Training.objects.all()
    serializer_class = TrainingSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'trainingCategory__id', 'startDate', 'endDate']
    filterset_fields = ['name', 'trainingCategory__id', 'startDate', 'endDate', 'status']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>List of Training</h1>
        """
        name = request.query_params.get('name')
        trainingCategory__id = request.query_params.get('trainingCategory__id')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        status = request.query_params.get('status')
        registerStartDate = request.query_params.get('registerStartDate')
        registerEndDate = request.query_params.get('registerEndDate')
        ordering = request.query_params.get('ordering')
        
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if trainingCategory__id:
            queryset = queryset.filter(trainingCategory__id=trainingCategory__id)
        if startDate:
            queryset = queryset.filter(startDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(endDate__lte=convert_str_to_date_max_time(endDate))
        if registerStartDate:
            queryset = queryset.filter(registerStartDate__gte=convert_str_to_date_min_time(registerStartDate))
        if registerEndDate:
            queryset = queryset.filter(registerEndDate__lte=convert_str_to_date_max_time(registerEndDate))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)
        
    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลอบรม</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of Training IDs to include in the export
            "name": "ชื่ออบรม"
            "trainingCategory__id": "ประเภทอบรม"
            "startDate": "วันที่เริ่มต้น"
            "endDate": "วันที่สิ้นสุด"
            "status": "สถานะ"
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        name = request.data.get('name')
        trainingCategory__id = request.data.get('trainingCategory__id')
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        status = request.data.get('status')
        registerStartDate = request.data.get('registerStartDate')
        registerEndDate = request.data.get('registerEndDate')
        
        queryset = self.get_queryset()
        if name:
            queryset = queryset.filter(name__icontains=name)
        if trainingCategory__id:
            queryset = queryset.filter(trainingCategory__id=trainingCategory__id)
        if startDate:
            queryset = queryset.filter(startDate__gte=format_date(startDate))
        if endDate:
            queryset = queryset.filter(endDate__lte=format_date(endDate))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if registerStartDate:
            queryset = queryset.filter(registerStartDate__gte=convert_str_to_date_min_time(registerStartDate))
        if registerEndDate:
            queryset = queryset.filter(registerEndDate__lte=convert_str_to_date_max_time(registerEndDate))
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)

        # Thai month abbreviations
        thai_months = {
            1: 'ม.ค.',
            2: 'ก.พ.',
            3: 'มี.ค.',
            4: 'เม.ย.',
            5: 'พ.ค.',
            6: 'มิ.ย.',
            7: 'ก.ค.',
            8: 'ส.ค.',
            9: 'ก.ย.',
            10: 'ต.ค.',
            11: 'พ.ย.',
            12: 'ธ.ค.'
        }
        
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)
        
        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            startDate = f'{item.startDate.day} {thai_months[item.startDate.month]} {item.startDate.year + 543}'
            endDate = f'{item.endDate.day} {thai_months[item.endDate.month]} {item.endDate.year + 543}'
            excel_data.append({
                'ลำดับ': count,
                'ชื่อการอบรม': item.name,
                'วันที่อบรม': f"{startDate} - {endDate}",
                'หมวดหมู่': item.trainingCategory.name if item.trainingCategory else "",
                'สถานะ': 'ใช้งาน' if item.status else 'ไม่ใช้งาน',
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลอบรม'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:H1', sheet_name, header_format)
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:H2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
