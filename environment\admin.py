from django.contrib import admin
from .models import Environment


@admin.register(Environment)
class EnvironmentAdmin(admin.ModelAdmin):
    """Admin configuration for Environment model"""

    list_display = [
        'id', 'parameter', 'value', 'unit', 'measurementDate',
        'route', 'masProvince', 'masDistrict', 'complianceStandard'
    ]
    list_filter = [
        'parameter', 'unit', 'measurementDate', 'masProvince', 'masDistrict',
        'complianceStandard'
    ]
    search_fields = [
        'parameter', 'unit', 'complianceStandard', 'remark',
        'route__name', 'masProvince__name', 'masDistrict__name'
    ]
    date_hierarchy = 'measurementDate'
    ordering = ['-measurementDate']

    fieldsets = (
        ('Basic Information', {
            'fields': ('route', 'masProvince', 'masDistrict')
        }),
        ('Measurement Data', {
            'fields': ('parameter', 'value', 'unit', 'measurementDate')
        }),
        ('Compliance & Notes', {
            'fields': ('complianceStandard', 'remark'),
            'classes': ('collapse',)
        }),
    )
