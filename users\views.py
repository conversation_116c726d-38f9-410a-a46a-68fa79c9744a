from django.db import connection
from drf_spectacular.utils import extend_schema, OpenApiResponse
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import AllowAny, IsAuthenticated, IsAdminUser, IsAuthenticatedOrReadOnly
from django.contrib.auth.hashers import make_password
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from RTRDA.middleware import get_current_user_id
from utils.pagination import CustomPagination
from django_filters import filters
from django_filters.rest_framework import DjangoFilterBackend
from utils.util import *
from django.conf import settings
from django.db.models import Q

from .models import User, Userlog, Permission, PermissionRole, PermissionUser, Role, UserRole
from .serializers import (
    UserSerializer,
    RegisterRequestSerializer,
    TokenResponseSerializer,
    UserlogSerializer,
    PermissionSerializer,
    PermissionRoleSerializer,
    PermissionUserSerializer,
    RoleSerializer,
    UserRoleSerializer,
)
from rest_framework.parsers import <PERSON>PartParser, FormParser, JSONParser
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from django.http import HttpResponse


@extend_schema(
    tags=["user"]
)
class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet for User model providing CRUD operations and additional actions.
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    pagination_class = CustomPagination
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    authentication_classes = [JWTAuthentication]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['firstname', 'lastname', 'email', 'phoneNumber', 'status']

    def get_permissions(self):
        """
        Override to set custom permissions for different actions
        """
        if self.action in ['register']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def list(self, request, *args, **kwargs):
        """
        List of users
        Parameters:
        - firstname: str
        - lastname: str
            - in case fullname search, firstname and lastname is required
        - email: str
        - phoneNumber: str
        - permission: int (permission id)
        - status: boolean
        - ordering: str
        """
        queryset = self.get_queryset()
        firstname = request.query_params.get('firstname')
        lastname = request.query_params.get('lastname')
        email = request.query_params.get('email')
        phoneNumber = request.query_params.get('phoneNumber')
        permission = request.query_params.get('permission')
        status = request.query_params.get('status')
        ordering = request.query_params.get('ordering', 'id')
        if firstname and lastname:
            if len(firstname.split(' ')) > 1:
                fullname = firstname.split(' ')
                firstname = fullname[0]
                lastname = fullname[1]
                queryset = queryset.filter(firstname__icontains=firstname, lastname__icontains=lastname)
            else:
                queryset = queryset.filter(Q(firstname__icontains=firstname) | Q(lastname__icontains=lastname))
        elif firstname:
            queryset = queryset.filter(firstname__icontains=firstname)
        elif lastname:
            queryset = queryset.filter(lastname__icontains=lastname)
        if email:
            queryset = queryset.filter(email__icontains=email)
        if phoneNumber:
            queryset = queryset.filter(phoneNumber__icontains=phoneNumber)
        if permission:
            permission_users = PermissionUser.objects.filter(permission__id=permission)
            queryset = queryset.filter(id__in=permission_users.values_list('user', flat=True))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if ordering:
            queryset = queryset.order_by(ordering)
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        data = serializer.data

        # Add permissions to each user in the serialized data
        for i, user_data in enumerate(data):
            user_id = user_data['id']
            permission_users = PermissionUser.objects.filter(user_id=user_id)
            if permission_users:
                user_data['permission'] = PermissionSerializer(
                    permission_users[0].permission).data

        return self.get_paginated_response(data)

    @extend_schema(
        description="Register a new user",
        request=RegisterRequestSerializer,
        responses={
            201: TokenResponseSerializer,
            400: OpenApiResponse(description="Invalid input")
        }
    )
    @action(detail=False, methods=['post'], url_path='register')
    def register(self, request):
        """Register a new user"""
        serializer = RegisterRequestSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            if User.objects.filter(email=data['email']).exists():
                return Response({"error": "Email นี้มีในระบบแล้ว"}, status=status.HTTP_400_BAD_REQUEST)
            user = User.objects.create_user(
                email=data['email'],
                password=data['password'],
                firstname=data.get('firstname', ''),
                lastname=data.get('lastname', ''),
                phoneNumber=data.get('phoneNumber', ''),
                image=data.get('image', ''),
            )

            refresh = RefreshToken.for_user(user)
            return Response({
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "user": UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['GET'], url_path='get-data')
    def get_data(self, request):
        """Get current user"""
        user_id = get_current_user_id()
        user = User.objects.get(id=user_id)
        # Get user permissions
        permission_users = PermissionUser.objects.filter(user__id=user_id)
        permissions = [pu.permission for pu in permission_users]
        # Get user roles
        for permission in permissions:
            permission_roles = PermissionRole.objects.filter(permission__id=permission.id)
            roles = [ur.role for ur in permission_roles]

        # Create user data with permissions
        user_data = UserSerializer(user).data
        user_data['permission'] = PermissionSerializer(
            permissions, many=True).data
        user_data['role'] = RoleSerializer(roles, many=True).data
        if user.image:
            base_url = settings.MEDIA_URL
            if not base_url.startswith('http'):
                base_url = request.build_absolute_uri('/').rstrip('/') + '/' + settings.MEDIA_PREFIX
            user_data['image'] = f"{base_url}{user.image}"
        return Response(user_data)

    def update(self, request, *args, **kwargs):
        """Update user"""
        pk = kwargs.get('pk')
        if pk:
            # If updating a specific user by ID (admin functionality)
            try:
                user = User.objects.get(id=pk)
            except User.DoesNotExist:
                return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get data from request
        firstname = request.data.get('firstname')
        lastname = request.data.get('lastname')
        email = request.data.get('email')
        phoneNumber = request.data.get('phoneNumber')
        image = request.data.get('image')
        password = request.data.get('password')
        status = request.data.get('status')

        # Update fields if provided and changed
        if firstname and firstname != user.firstname:
            user.firstname = firstname
        if lastname and lastname != user.lastname:
            user.lastname = lastname
        if email and email != user.email:
            if User.objects.filter(email=email).exclude(id=user.id).exists():
                return Response({"error": "Email นี้มีในระบบแล้ว"}, status=status.HTTP_400_BAD_REQUEST)
            else:
                if validate_email(email):
                    user.email = email
                else:
                    return Response({"error": "รูปแบบอีเมลไม่ถูกต้อง"}, status=status.HTTP_400_BAD_REQUEST)
        if phoneNumber and phoneNumber != user.phoneNumber:
            if validate_phone_number(phoneNumber):
                user.phoneNumber = phoneNumber
            else:
                return Response({"error": "รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง"}, status=status.HTTP_400_BAD_REQUEST)
        if image and not str(image).startswith('http'):
            user.image = image
        if password:
            user.password = password
        if status:
            user.status = convert_str_to_bool(status)
        # Save the user
        user.save()

        # Return the updated user data
        return Response(UserSerializer(user).data)

    def perform_destroy(self, instance):
        """
        Override to handle user deletion without relying on django_admin_log table
        Instead of using the default delete which goes through collectors,
        we manually handle the delete to avoid dependencies on admin tables.
        """
        # First delete related records
        UserRole.objects.filter(user=instance).delete()
        PermissionUser.objects.filter(user=instance).delete()
        Userlog.objects.filter(user=instance).delete()
        
        # Then delete the user directly with raw SQL to bypass Django's collector
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM [User] WHERE Id = %s", [instance.id])

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลผู้ใช้งาน</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of User IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        firstname = request.data.get('firstname')
        lastname = request.data.get('lastname')
        email = request.data.get('email')
        phoneNumber = request.data.get('phoneNumber')
        permission = request.data.get('permission')
        status = request.data.get('status')
         
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if firstname and lastname:
            if len(firstname.split(' ')) > 1:
                fullname = firstname.split(' ')
                firstname = fullname[0]
                lastname = fullname[1]
                queryset = queryset.filter(firstname__icontains=firstname, lastname__icontains=lastname)
            else:
                queryset = queryset.filter(Q(firstname__icontains=firstname) | Q(lastname__icontains=lastname))
        elif firstname:
            queryset = queryset.filter(firstname__icontains=firstname)
        elif lastname:
            queryset = queryset.filter(lastname__icontains=lastname)
        if email:
            queryset = queryset.filter(email__icontains=email)
        if phoneNumber:
            queryset = queryset.filter(phoneNumber__icontains=phoneNumber)
        if permission:
            permission_users = PermissionUser.objects.filter(permission__id=permission)
            queryset = queryset.filter(id__in=permission_users.values_list('user', flat=True))
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
            
        for user in queryset:
            permission_users = PermissionUser.objects.filter(user__id=user.id)
            permissions = [pu.permission for pu in permission_users]
                
        # Convert data to DataFrame
        excel_data = []
        for index, item in enumerate(queryset):
            excel_data.append({
                'ลำดับ': index + 1,
                'อีเมล': item.email,
                'ชื่อ-นามสกุล': item.firstname + ' ' + item.lastname,
                'กลุ่มสิทธิ์': permissions[0].name if permissions else '',
                'ข้อมูลประเภทเจ้าหน้าที่': 'เจ้าหน้าที่' if item.userType == 'O' else 'สมาชิก',
                'สถานะ': 'ใช้งาน' if item.status else 'ไม่ใช้งาน',
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้ใช้งาน'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:F1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:F2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["user"]
)
class UserlogViewSet(viewsets.ModelViewSet):
    queryset = Userlog.objects.all()
    serializer_class = UserlogSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['detail']
    
    def create(self, request, *args, **kwargs):
        """Save user logs"""
        # Debug authentication
        print("Authentication:", request.META.get(
            'HTTP_AUTHORIZATION', 'No Authorization header'))
        print("User authenticated:", request.user.is_authenticated if hasattr(
            request, 'user') else "No user")
        print("User:", request.user if hasattr(request, 'user') else "No user")

        # Get browser information from request headers
        user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
        # Truncate user_agent to 100 characters to prevent database truncation error
        user_agent = user_agent[:100] if user_agent else 'Unknown'

        # Get IP address from request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0].strip()
        else:
            ip_address = request.META.get('REMOTE_ADDR', 'Unknown')

        # Get user object from user ID
        user_id = get_current_user_id()
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            # Use a default user or return an error response
            return Response(
                {"error": f"User with ID {user_id} does not exist"},
                status=status.HTTP_400_BAD_REQUEST
            )

        userlog = Userlog(
            user=user,
            ipAddress=ip_address,
            browser=user_agent,
            header=request.data.get('header'),
            detail=request.data.get('detail'),
            platform=request.data.get('platform'),
            version=request.data.get('version'),
        )

        userlog.save()

        # Serialize and return the created userlog
        serializer = self.get_serializer(userlog)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        userlog = Userlog.objects.filter()
        detail = request.query_params.get('detail')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        permissionId = request.query_params.get('permission__id')
        ordering = request.query_params.get('ordering', '-createDate')
        if detail:
            userlog = userlog.filter(header__icontains=detail)
        if startDate:
            userlog = userlog.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            userlog = userlog.filter(createDate__lte=convert_str_to_date_max_time(endDate))
        if permissionId:
            permission_users = PermissionUser.objects.filter(permission__id=permissionId)
            userlog = userlog.filter(user__in=permission_users.values_list('user', flat=True))
        
        # Apply ordering
        userlog = userlog.order_by(ordering)
        
        # Paginate the results
        page = self.paginate_queryset(userlog)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
            
            # Add permission information to each user log
            for log_entry in data:
                user_id = log_entry.get('user').get('id')
                if user_id:
                    permission_user = PermissionUser.objects.filter(user__id=user_id).first()
                    if permission_user:
                        log_entry['permission'] = PermissionSerializer(permission_user.permission).data
                    
            return self.get_paginated_response(data)
            
        serializer = self.get_serializer(userlog, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['GET'], url_path='detail/(?P<user_id>[0-9]+)')
    def get_by_user(self, request, *args, **kwargs):
        user_id = kwargs.get('user_id')
        userlog = Userlog.objects.filter(user__id=user_id)
        order_by = request.query_params.get('order_by', '-createDate')
        userlog = userlog.order_by(order_by)
        page = self.paginate_queryset(userlog)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลบันทึกการใช้งาน</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of UserLog IDs to include in the export
            "permission__id": 1  # Permission ID to include in the export
            "detail": "รายละเอียด"  # Detail to include in the export
            "startDate": "2021-01-01"  # Start Date to include in the export
            "endDate": "2021-01-01"  # End Date to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        permission_id = request.data.get('permission__id')
        detail = request.data.get('detail')
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if permission_id:
            permission_users = PermissionUser.objects.filter(permission__id=permission_id)
            queryset = queryset.filter(user__in=permission_users.values_list('user', flat=True))
        if detail:
            queryset = queryset.filter(detail__icontains=detail)
        if startDate:
            queryset = queryset.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(createDate__lte=convert_str_to_date_max_time(endDate))
            
                
        # Convert data to DataFrame
        excel_data = []
        for index, item in enumerate(queryset):
            permission_users = PermissionUser.objects.filter(user__id=item.user.id)
            permissions = [pu.permission for pu in permission_users]
            excel_data.append({
                'ลำดับ': index + 1,
                'วัน/เวลา': convert_date_to_str_thai(item.createDate,with_time=True),
                'ชื่อ-นามสกุล': item.user.firstname + ' ' + item.user.lastname,
                'กลุ่มสิทธิ์': permissions[0].name if permissions else '',
                'หัวข้อ': item.header,
                'รายละเอียด': item.detail,
                'อุปกรณ์': item.browser,
            })
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'รายการบันทึกการใช้งานเจ้าหน้าที่'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:G1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:G2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


@extend_schema(
    tags=["user"]
)
class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticated]


@extend_schema(
    tags=["user"]
)
class PermissionViewSet(viewsets.ModelViewSet):
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
        else:
            serializer = self.get_serializer(queryset, many=True)

        result_data = []

        for data in serializer.data:
            permission_id = data['id']
            permission_roles = PermissionRole.objects.filter(
                permission=permission_id)

            # Create a copy of the data to avoid modifying the original
            permission_data = dict(data)

            # Serialize the related objects
            permission_data['permissionRole'] = PermissionRoleSerializer(
                permission_roles, many=True).data

            result_data.append(permission_data)

        # Return paginated response if pagination is applied
        if page is not None:
            return self.get_paginated_response(result_data)

        # Otherwise return regular response
        return Response(result_data)

    def update(self, request, *args, **kwargs):
        name = request.data.get('name')
        if name:
            permission = Permission.objects.get(id=kwargs.get('pk'))
            if permission.name != name:
                permission.name = name
                permission.save()
                return Response({"message": "Permission updated successfully"}, status=status.HTTP_200_OK)
            else:
                return Response({"message": "Permission no need to update"}, status=status.HTTP_200_OK)
        return Response({"message": "Permission not found"}, status=status.HTTP_404_NOT_FOUND)


@extend_schema(
    tags=["user"]
)
class PermissionRoleViewSet(viewsets.ModelViewSet):
    queryset = PermissionRole.objects.all()
    serializer_class = PermissionRoleSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['PUT'], url_path='update')
    def update_permission_role(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            permission_id = request.data.get('permissionId')
            try:
                # Find an existing instance to update, or create a dummy one
                instance = PermissionRole.objects.filter(
                    permission_id=permission_id).first()
                if instance:
                    # Update existing instance
                    serializer.update(instance, serializer.validated_data)
                else:
                    # Create a new instance using the create method
                    serializer.create(serializer.validated_data)

                return Response({"message": "Permission roles updated successfully"}, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({"message": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Failed to update permission roles"}, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    tags=["user"]
)
class PermissionUserViewSet(viewsets.ModelViewSet):
    queryset = PermissionUser.objects.all()
    serializer_class = PermissionUserSerializer
    pagination_class = CustomPagination

    @action(detail=False, methods=['PUT'], url_path='update', parser_classes=[JSONParser])
    def update_permission_user(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            permission_id = request.data.get('permissionId')
            user_id = request.data.get('userId')
            try:
                instance = PermissionUser.objects.filter(user_id=user_id).first()
                if instance:
                    instance.permission_id = permission_id
                    instance.save()
                else:
                    permission_user = PermissionUser.objects.create(user_id=user_id, permission_id=permission_id)
                    permission_user.save()
                return Response({"message": "Permission user updated successfully"}, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({"message": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Failed to update permission user"}, status=status.HTTP_400_BAD_REQUEST)


