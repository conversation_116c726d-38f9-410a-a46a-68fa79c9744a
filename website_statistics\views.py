from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from .models import Websitestatistics, WebsiteStatisticsChartData
from .serializers import WebsitestatisticsSerializer, WebsiteStatisticsChartDataSerializer
from utils.pagination import CustomPagination
from rest_framework.response import Response
from django.db.models import Count
from rest_framework.decorators import action
from rest_framework import status
from .statistic_name import statistic_website
from drf_spectacular.utils import extend_schema
from datetime import datetime, timedelta
from rest_framework.test import APIRequestFactory
from rest_framework.request import Request
import locale
from django.utils import timezone
from babel.dates import format_date
from django.conf import settings
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time, convert_date_to_str_thai
import pandas as pd
import io
from django.http import HttpResponse
import urllib.parse
import io

@extend_schema(
    tags=["Website Statistics"]
)
class WebsitestatisticsViewSet(viewsets.ModelViewSet):
    queryset = Websitestatistics.objects.all()
    serializer_class = WebsitestatisticsSerializer
    pagination_class = CustomPagination
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        page_size = int(request.query_params.get('page_size', 6))
        # Group by page and count occurrences, order by count in descending order
        db_statistics = Websitestatistics.objects.values('page') \
            .annotate(count=Count('page')) \
            .order_by('-count')

        # Convert queryset to dictionary for easier lookup
        stats_dict = {item['page']: item['count'] for item in db_statistics}

        base_url = settings.MEDIA_URL
        if not base_url.startswith('http'):
            base_url = request.build_absolute_uri('/').rstrip('/') + '/' + settings.MEDIA_PREFIX
            
        # Add statistic_name to stats_dict
        result_dict = {}
        for page, count in stats_dict.items():
            # Check if the page exists in statistic_website dictionary
            if page in statistic_website:
                result_dict[page] = {
                    'page': statistic_website[page]['name'],
                    'name': page,
                    'count': count,
                    'icon':  f"{base_url}static/icons/{statistic_website[page]['icon']}" if statistic_website[page]['icon'] else f"{base_url}static/icons/default.svg",
                    'url': statistic_website[page]['url']
                }
            else:
                # Handle pages that don't exist in statistic_website dictionary
                result_dict[page] = {
                    'page': page,
                    'name': page,
                    'count': count,
                    'icon':  f"{base_url}static/icons/default.svg",
                    'url': '/'
                }

        # Convert dictionary to list of dictionaries for sorting
        stats_list = list(result_dict.values())

        # Sort by count in descending order
        stats_list = sorted(stats_list, key=lambda x: x['count'], reverse=True)

        sub_stats_list = stats_list[:page_size] if len(
            stats_list) > page_size else stats_list

        # Paginate the results
        page = self.paginate_queryset(sub_stats_list)
        if page is not None:
            return self.get_paginated_response(page)

    @action(detail=False, methods=['get'], url_path='click')
    def click_web_statistics(self, request):
        """
        <h1>Record web statistics when a user clicks on a link</h1>
        <h2>Parameters: (Querystring)</h2>
        <ul>
            <li>page: str</li>
            <li>platform: str</li>
            <li>version: str</li>
        </ul>
        <h2>Returns:</h2>
        <ul>
            <li>success: bool</li>
            <li>message: str</li>
        </ul>
        """
        platform = request.GET.get('platform')
        version = request.GET.get('version')
        page = request.GET.get('page')

        # Get browser information from request headers
        user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
        # Truncate user_agent to 100 characters to prevent database truncation error
        user_agent = user_agent[:100] if user_agent else 'Unknown'

        # Get IP address from request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0].strip()
        else:
            ip_address = request.META.get('REMOTE_ADDR', 'Unknown')

        # Create a new statistics record
        statistics = Websitestatistics(
            page=page,
            browser=user_agent,
            ipAddress=ip_address,
            platform=platform,
            version=version
        )
        statistics.save()

        # Return a serializable dictionary instead of the model instance
        return Response(status=status.HTTP_200_OK, data={
            'success': True,
            'message': 'Statistics recorded successfully'
        })

    @action(detail=False, methods=['GET'], url_path='chart-data')
    def get_chart_data(self, request):
        """
        <h1>ข้อมูลกราฟ</h1>
        <p>Parameter</p>
        <ul>
            <li>type: (Required)</li>
            <ul>
                <li>stats</li>
                <li>week</li>
                <li>month</li>
                <li>year</li>
                <li>summary</li>
            </ul>
            <li>startDate: (Required)</li>
            <li>endDate: (Required)</li>
            <li>labelOrder: asc or desc (Optional)</li>
        </ul>
        """
        chartData = WebsiteStatisticsChartData()
        labels = []
        datas = []
        type = request.query_params.get('type')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        labelOrder = request.query_params.get('labelOrder')
        backward = 7
        if type:
            if type == 'stats':
                if startDate and endDate:
                    statistics = Websitestatistics.objects.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_min_time(endDate))
                else:
                    statistics = Websitestatistics.objects.all()
                labels = ['จำนวนการเข้าชมวันนี้','จำนวนการเข้าชมทั้งหมด']
                datas = [0,0]
                for statistic in statistics:
                    if statistic.createDate.date() == datetime.now().date():
                        datas[0] += 1
                    datas[1] += 1
                chartData.label = labels
                chartData.data = datas
            elif type == 'week':
                statistics = Websitestatistics.objects.filter(createDate__date__gte=convert_str_to_date_min_time(startDate), createDate__date__lte=convert_str_to_date_min_time(endDate))
                chartData = WebsiteStatisticsChartData()
                labels = []
                datas = []
                backward = (convert_str_to_date_min_time(endDate) - convert_str_to_date_min_time(startDate)).days + 1
                for i in range(backward):
                    date_obj = convert_str_to_date_min_time(startDate) + timedelta(days=i)
                    # Convert to Thai date format with Buddhist Era year (BE = CE + 543)
                    thai_month_day = format_date(date_obj, format='dd MMM', locale='th_TH')
                    buddhist_year = date_obj.year + 543
                    thai_date = f"{thai_month_day} {buddhist_year}"
                    labels.append(thai_date)
                    datas.append(0)
                    for statistic in statistics:
                        if statistic.createDate.date() == date_obj.date():
                            datas[i] += 1
                
                if labelOrder and labelOrder == 'desc':
                    chartData.label = list(reversed(labels))
                    chartData.data = list(reversed(datas))
                else:
                    chartData.label = labels
                    chartData.data = datas
            elif type == 'month':
                # Get data for the past 7 months
                startDate = convert_str_to_date_min_time(startDate)
                endDate = convert_str_to_date_min_time(endDate)
                chartData = WebsiteStatisticsChartData()
                labels = []
                datas = []
                
                # Get the first day of the current month
                current_month_start = endDate.replace(day=1)
                backward = ((endDate.year - startDate.year) * 12) + (endDate.month - startDate.month) + 1
                for i in range(backward):
                    # Calculate the first day of each previous month
                    if i == 0:
                        month_start = current_month_start
                    else:
                        # Go to previous month
                        if month_start.month == 1:
                            month_start = month_start.replace(year=month_start.year-1, month=12, day=1)
                        else:
                            month_start = month_start.replace(month=month_start.month-1, day=1)
                    
                    # Calculate the last day of the month
                    if month_start.month == 12:
                        month_end = month_start.replace(year=month_start.year+1, month=1, day=1) - timedelta(days=1)
                    else:
                        month_end = month_start.replace(month=month_start.month+1, day=1) - timedelta(days=1)
                    
                    # Format the month name in Thai
                    thai_month = format_date(month_start, format='MMMM', locale='th_TH')
                    buddhist_year = month_start.year + 543
                    thai_date = f"{thai_month} {buddhist_year}"
                    
                    # Count statistics for this month
                    month_count = Websitestatistics.objects.filter(
                        createDate__date__gte=month_start.date(),
                        createDate__date__lte=month_end.date()
                    ).count()
                    
                    labels.append(thai_date)
                    datas.append(month_count)
                
                if labelOrder and labelOrder == 'desc':
                    chartData.label = list(reversed(labels))
                    chartData.data = list(reversed(datas))
                else:
                    chartData.label = labels
                    chartData.data = datas
            elif type == 'year':
                startDate = convert_str_to_date_min_time(startDate)
                endDate = convert_str_to_date_min_time(endDate)
                chartData = WebsiteStatisticsChartData()
                labels = []
                datas = []
                
                # Get the current year
                current_year = endDate.year
                backward = (endDate.year - startDate.year) + 1
                for i in range(backward):
                    # Calculate year
                    year = current_year - i
                    
                    # Start date is January 1st of the year
                    year_start = datetime(year, 1, 1).date()
                    
                    # End date is December 31st of the year
                    year_end = datetime(year, 12, 31).date()
                    
                    # Convert to Buddhist Era year (BE = CE + 543)
                    buddhist_year = year + 543
                    
                    # Count statistics for this year
                    year_count = Websitestatistics.objects.filter(
                        createDate__date__gte=year_start,
                        createDate__date__lte=year_end
                    ).count()
                    
                    labels.append(str(buddhist_year))
                    datas.append(year_count)
                
                if labelOrder and labelOrder == 'desc':
                    chartData.label = list(reversed(labels))
                    chartData.data = list(reversed(datas))
                else:
                    chartData.label = labels
                    chartData.data = datas
            elif type == 'summary':
                labels = [
                    'ภาพรวมด้านการบริการ',
                    'ข้อมูลโครงสร้างพื้นฐาน',
                    'ข้อมูลการเดินรถและการให้บริการ',
                    'ภาพรวมด้านอุตสาหกรรม',
                    'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย',
                    'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง',
                    'ข้อมูลมาตรฐานระบบราง',
                    'ข้อมูลศูนย์ทดสอบ',
                    'ภาพรวมด้านเทคโนโลยีฯ',
                    'ข้อมูลหลักสูตร',
                    'ข้อมูลการอบรม',
                    'ข้อมูลงานวิจัยและนวัตกรรม',
                ]
                # Get statistics data with a large page size to get all data
                statistics_data = self.get_statistics_data(page_size=-1, startDate=convert_str_to_date_min_time(startDate), endDate=convert_str_to_date_min_time(endDate))
                datas = [0] * len(labels) # Adjust to match the length of labels
                serviceOverAll = 0
                industrialOverAll = 0
                technologyOverAll = 0
                
                # Create a mapping between page names and their indices in the labels list
                page_to_index = {
                    'ข้อมูลโครงสร้างพื้นฐาน': 1,
                    'ข้อมูลการเดินรถและการให้บริการ': 2,
                    'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย': 4,
                    'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง': 5,
                    'ข้อมูลมาตรฐานระบบราง': 6,
                    'ข้อมูลศูนย์ทดสอบ': 7,
                    'ข้อมูลหลักสูตร': 9,
                    'ข้อมูลการอบรม': 10,
                    'ข้อมูลงานวิจัยและนวัตกรรม': 11,
                }
                
                for statistic in statistics_data:
                    page_name = statistic['page']
                    if page_name in page_to_index:
                        index = page_to_index[page_name]
                        datas[index] += statistic['count']
                    if page_name == 'ข้อมูลโครงสร้างพื้นฐาน' or page_name == 'ข้อมูลการเดินรถและการให้บริการ':
                        serviceOverAll += statistic['count']
                    elif page_name == 'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย' or page_name == 'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง' or page_name == 'ข้อมูลมาตรฐานระบบราง' or page_name == 'ข้อมูลศูนย์ทดสอบ':
                        industrialOverAll += statistic['count']
                    elif page_name == 'ข้อมูลหลักสูตร' or page_name == 'ข้อมูลการอบรม' or page_name == 'ข้อมูลงานวิจัยและนวัตกรรม':
                        technologyOverAll += statistic['count']
                datas[0] = serviceOverAll
                datas[3] = industrialOverAll
                datas[8] = technologyOverAll
                chartData.label = labels
                chartData.data = datas
        else:
            return Response({'error': 'type is required'})
        return Response(WebsiteStatisticsChartDataSerializer(chartData).data)

    def get_statistics_data(self, page_size=6, startDate=None, endDate=None):
        """Helper method to get statistics data with a specific page size"""
        if startDate and endDate:
            statistics_data = Websitestatistics.objects.filter(createDate__date__gte=startDate, createDate__date__lte=endDate)
        else:
            statistics_data = Websitestatistics.objects.all()
        # Group by page and count occurrences, order by count in descending order
        db_statistics = statistics_data.values('page') \
            .annotate(count=Count('page')) \
            .order_by('-count')

        # Convert queryset to dictionary for easier lookup
        stats_dict = {item['page']: item['count'] for item in db_statistics}

        # Add statistic_name to stats_dict
        result_dict = {}
        for page, count in stats_dict.items():
            # Check if the page exists in statistic_website dictionary
            if page in statistic_website:
                result_dict[page] = {
                    'page': statistic_website[page]['name'],
                    'name': page,
                    'count': count,
                    'icon': "",  # Simplified for this method
                    'url': statistic_website[page]['url']
                }
            else:
                # Handle pages that don't exist in statistic_website dictionary
                result_dict[page] = {
                    'page': page,
                    'name': page,
                    'count': count,
                    'icon': "",  # Simplified for this method
                    'url': '/'
                }

        # Convert dictionary to list of dictionaries for sorting
        stats_list = list(result_dict.values())

        # Sort by count in descending order
        stats_list = sorted(stats_list, key=lambda x: x['count'], reverse=True)

        # Return the full list or a subset based on page_size
        return stats_list[:page_size] if page_size != -1 else stats_list

    @action(detail=False, methods=['GET'], url_path='chart-data-daily')
    def get_chart_data_daily(self, request):
        """
        <h1>ข้อมูลกราฟตามวัน</h1>
        <p>Parameter</p>
        <ul>
            <li>days: (Required)</li>
        </ul>
        """
        days = request.query_params.get('days')
        if days:
            days = int(days)
            statistics = Websitestatistics.objects.filter(createDate__date__gte=datetime.now().date() - timedelta(days=days))
            chartData = WebsiteStatisticsChartData()
            labels = []
            datas = []
            for i in range(days):
                date_obj = datetime.now().date() - timedelta(days=i)
                # Convert to Thai date format with Buddhist Era year (BE = CE + 543)
                thai_month_day = format_date(date_obj, format='dd MMM', locale='th_TH')
                buddhist_year = date_obj.year + 543
                thai_date = f"{thai_month_day} {buddhist_year}"
                labels.append(thai_date)
                datas.append(0)
                for statistic in statistics:
                    if statistic.createDate.date() == date_obj:
                        datas[i] += 1
            # Reverse the labels and data to show oldest to newest
            chartData.label = list(reversed(labels))
            chartData.data = list(reversed(datas))
            return Response(WebsiteStatisticsChartDataSerializer(chartData).data)
        else:
            return Response({'error': 'days is required'})
    
    @action(detail=False, methods=['post'], url_path='export-excel', permission_classes=[AllowAny])
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลสถิติการเข้าใช้งาน</h1>
        <ul>
            <li>startDate: (Required)</li>
            <li>endDate: (Required)</li>
        </ul>
        """
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        queryset = self.get_queryset()
        if startDate:
            queryset = queryset.filter(createDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(createDate__lte=convert_str_to_date_max_time(endDate))
            
        # Convert data to DataFrame
        excel_data = []
        
        # Create a continuous range of dates between startDate and endDate
        start_date = convert_str_to_date_min_time(startDate).date()
        end_date = convert_str_to_date_max_time(endDate).date()
        list_of_date = [start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)]
        
        # Initialize list_of_data with the correct dimensions
        # Create a 2D array with rows = number of dates, columns = 12 (number of categories)
        list_of_data = [[0 for _ in range(12)] for _ in range(len(list_of_date))]
        
        # Process each item and update counts
        for item in queryset:
            if item.page:
                # Find the index of the date
                if item.createDate.date() in list_of_date:
                    index = list_of_date.index(item.createDate.date())
                    if item.page == 'Infrastructure':
                        list_of_data[index][0] += 1  # ภาพรวมด้านการบริการ
                        list_of_data[index][1] += 1  # ข้อมูลโครงสร้างพื้นฐาน
                    elif item.page == 'Transportation':
                        list_of_data[index][0] += 1  # ภาพรวมด้านการบริการ
                        list_of_data[index][2] += 1  # ข้อมูลการเดินรถและการให้บริการ
                    elif item.page == 'RailwayIndustrial':
                        list_of_data[index][3] += 1  # ภาพรวมด้านอุตสาหกรรม
                        list_of_data[index][4] += 1  # ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย
                    elif item.page == 'PartEquipment':
                        list_of_data[index][3] += 1  # ภาพรวมด้านอุตสาหกรรม
                        list_of_data[index][5] += 1  # ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง
                    elif item.page == 'Standard':
                        list_of_data[index][3] += 1  # ภาพรวมด้านอุตสาหกรรม
                        list_of_data[index][6] += 1  # ข้อมูลมาตรฐานระบบราง
                    elif item.page == 'TestingCenter':
                        list_of_data[index][3] += 1  # ภาพรวมด้านอุตสาหกรรม
                        list_of_data[index][7] += 1  # ข้อมูลศูนย์ทดสอบ
                    elif item.page == 'Course':
                        list_of_data[index][8] += 1  # ภาพรวมด้านเทคโนโลยีฯ
                        list_of_data[index][9] += 1  # ข้อมูลหลักสูตร
                    elif item.page == 'Training':
                        list_of_data[index][8] += 1  # ภาพรวมด้านเทคโนโลยีฯ
                        list_of_data[index][10] += 1 # ข้อมูลการอบรม
                    elif item.page == 'Research':
                        list_of_data[index][8] += 1  # ภาพรวมด้านเทคโนโลยีฯ
                        list_of_data[index][11] += 1 # ข้อมูลงานวิจัยและนวัตกรรม
        
        # Build excel data
        for i, list_date in enumerate(list_of_date):
            excel_data.append({
                'วันที่': convert_date_to_str_thai(list_date),
                'ภาพรวมด้านการบริการ': list_of_data[i][0],
                'ข้อมูลโครงสร้างพื้นฐาน': list_of_data[i][1],
                'ข้อมูลการเดินรถและการให้บริการ': list_of_data[i][2],
                'ภาพรวมด้านอุตสาหกรรม': list_of_data[i][3],
                'ข้อมูลผู้ผลิตชิ้นส่วนระบบรางในประเทศไทย': list_of_data[i][4],
                'ข้อมูลชิ้นส่วนและอุปกรณ์ของระบบราง': list_of_data[i][5],
                'ข้อมูลมาตรฐานระบบราง': list_of_data[i][6],
                'ข้อมูลศูนย์ทดสอบ': list_of_data[i][7],
                'ภาพรวมด้านเทคโนโลยีฯ': list_of_data[i][8],
                'ข้อมูลหลักสูตร': list_of_data[i][9],
                'ข้อมูลการอบรม': list_of_data[i][10],
                'ข้อมูลงานวิจัยและนวัตกรรม': list_of_data[i][11],
            })

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลรายงานสถิติการเข้าใช้งาน'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=3)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:M1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Add date range header
            # Parse dates flexibly to handle different formats
            try:
                # Try different date formats
                date_formats = ['%Y-%m-%d', '%d-%m-%Y', '%d/%m/%Y']
                start_date_obj = None
                end_date_obj = None
                
                for fmt in date_formats:
                    try:
                        start_date_obj = datetime.strptime(startDate, fmt)
                        break
                    except ValueError:
                        continue
                
                for fmt in date_formats:
                    try:
                        end_date_obj = datetime.strptime(endDate, fmt)
                        break
                    except ValueError:
                        continue
                
                # If we couldn't parse the dates, use safer defaults
                if start_date_obj is None:
                    # Try to extract dates from the original filtering
                    start_date_obj = convert_str_to_date_min_time(startDate)
                
                if end_date_obj is None:
                    end_date_obj = convert_str_to_date_max_time(endDate)
                
                # Convert to Thai Buddhist Era year
                start_thai_year = start_date_obj.year + 543
                end_thai_year = end_date_obj.year + 543
                
                # Format dates in Thai style
                start_thai_date = f"{start_date_obj.day} {thai_months[start_date_obj.month]} {start_thai_year}"
                end_thai_date = f"{end_date_obj.day} {thai_months[end_date_obj.month]} {end_thai_year}"
                
                date_range = f"ช่วงวันที่ : {start_thai_date} - {end_thai_date}"
            except Exception as e:
                # Fallback to raw strings if there's any error in parsing
                date_range = f"ช่วงวันที่ : {startDate} - {endDate}"
            
            date_range_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:M2', date_range, date_range_format)
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A3:M3', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                max_width = len(col) * 1.5  # Start with column header width for Thai characters
                
                for value in df[col]:
                    if isinstance(value, (int, float)):  # Handle numeric values
                        max_width = max(max_width, len(str(value)) + 2)
                    elif value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Calculate width based on character types
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            # Thai characters need more width
                            adjusted_width = latin_char_count + (thai_char_count * 2)
                            max_width = max(max_width, adjusted_width)
                
                # Set column width with some padding (minimum 8, maximum 50)
                worksheet.set_column(i, i, min(max(8, max_width + 2), 50))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(4, len(df) + 4):  # row 5 ถึง len(df)+4
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-4, col_num]  # row_num-4 = 0 เมื่อ row_num=4
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max(15, sum(1 for value in df.iloc[row_num-4] if isinstance(value, str)) * 15)
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

