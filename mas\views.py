from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from .models import (
    MasAgeRange, MasCourseType, MasDistrict,
    MasGeography, MasProvince, MasPosition,
    MasStandardAgency, MasStandardCategory, MasRailComponentType, MasRailwayLine,
    MasSubdistrict, MasTrainType, MasTypeOfWork,
    MasUsagePurpose, MasUserType, Standard, StandardMasStandardCategory
)
from .serializers import (
    MasAgeRangeSerializer, MasCourseTypeSerializer,
    MasDistrictSerializer, MasGeographySerializer,
    MasProvinceSerializer, MasPositionSerializer,
    MasStandardAgencySerializer, MasStandardCategorySerializer, MasRailComponentTypeSerializer,
    MasRailwayLineSerializer, MasSubdistrictSerializer,
    MasTrainTypeSerializer, MasTypeOfWorkSerializer,
    MasUsagePurposeSerializer, MasUserTypeSerializer,
    StandardSerializer, StandardMasStandardCategorySerializer
)
from testings.models import StandardTestingCenter
from components.models import StandardComponent
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from utils.pagination import CustomPagination
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from utils.util import convert_str_to_bool
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
import io
import pandas as pd
from datetime import datetime
import urllib.parse
from django.http import HttpResponse
from rest_framework import status
from users.models import User

@extend_schema(
    tags=["Master Data"]
)
class MasAgeRangeViewSet(viewsets.ModelViewSet):
    queryset = MasAgeRange.objects.all()
    serializer_class = MasAgeRangeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasCourseTypeViewSet(viewsets.ModelViewSet):
    queryset = MasCourseType.objects.all()
    serializer_class = MasCourseTypeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasGeographyViewSet(viewsets.ModelViewSet):
    queryset = MasGeography.objects.all()
    serializer_class = MasGeographySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasProvinceViewSet(viewsets.ModelViewSet):
    queryset = MasProvince.objects.all()
    serializer_class = MasProvinceSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]

@extend_schema(
    tags=["Master Data"]
)
class MasPositionViewSet(viewsets.ModelViewSet):
    queryset = MasPosition.objects.all()
    serializer_class = MasPositionSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasDistrictViewSet(viewsets.ModelViewSet):
    queryset = MasDistrict.objects.all()
    serializer_class = MasDistrictSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['masProvince__id']
    
    def list(self, request, *args, **kwargs):
        masProvince__id = request.query_params.get('masProvince__id')
        queryset = self.get_queryset()
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            return super().list(request, *args, **kwargs)

@extend_schema(
    tags=["Master Data"]
)
class MasStandardAgencyViewSet(viewsets.ModelViewSet):
    queryset = MasStandardAgency.objects.all()
    serializer_class = MasStandardAgencySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasStandardCategoryViewSet(viewsets.ModelViewSet):
    queryset = MasStandardCategory.objects.all()
    serializer_class = MasStandardCategorySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['name']
    ordering_fields = ['name']

@extend_schema(
    tags=["Master Data"]
)
class MasRailComponentTypeViewSet(viewsets.ModelViewSet):
    queryset = MasRailComponentType.objects.all()
    serializer_class = MasRailComponentTypeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasRailwayLineViewSet(viewsets.ModelViewSet):
    queryset = MasRailwayLine.objects.all()
    serializer_class = MasRailwayLineSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasSubdistrictViewSet(viewsets.ModelViewSet):
    queryset = MasSubdistrict.objects.all()
    serializer_class = MasSubdistrictSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['masDistrict__id']
    
    def list(self, request, *args, **kwargs):
        masDistrict__id = request.query_params.get('masDistrict__id')
        queryset = self.get_queryset()
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        else:
            return super().list(request, *args, **kwargs)

@extend_schema(
    tags=["Master Data"]
)
class MasTrainTypeViewSet(viewsets.ModelViewSet):
    queryset = MasTrainType.objects.all()
    serializer_class = MasTrainTypeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasTypeOfWorkViewSet(viewsets.ModelViewSet):
    queryset = MasTypeOfWork.objects.all()
    serializer_class = MasTypeOfWorkSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasUsagePurposeViewSet(viewsets.ModelViewSet):
    queryset = MasUsagePurpose.objects.all()
    serializer_class = MasUsagePurposeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class MasUserTypeViewSet(viewsets.ModelViewSet):
    queryset = MasUserType.objects.all()
    serializer_class = MasUserTypeSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]

@extend_schema(
    tags=["Master Data"]
)
class StandardViewSet(viewsets.ModelViewSet):
    queryset = Standard.objects.all()
    serializer_class = StandardSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['masStandardAgency__code', 'name', 'masTypeOfWork__id', 'code', 'masRailComponentType__id', 'masTrainType__id']
    filterset_fields = ['masStandardAgency__code', 'name', 'masTypeOfWork__id', 'code', 'masRailComponentType__id', 'masTrainType__id']
    
    def list(self, request, *args, **kwargs):
        """
        <h1>List Standard data</h1>
        <h2>Parameters:(Querystring)</h2>
        <ul>
            <li>masStandardAgency__code: str</li>
            <li>name: str</li>
                <li>code: str</li>
            <li>masTypeOfWork__id: int</li>
        </ul>
        """
        masStandardAgency__code = request.query_params.get('masStandardAgency__code')
        name = request.query_params.get('name')
        code = request.query_params.get('code')
        masTypeOfWork__id = request.query_params.get('masTypeOfWork__id')
        status = request.query_params.get('status')
        testingCenterId = request.query_params.get('testingCenter__id')
        standardId = request.query_params.get('standard__id')
        id = request.query_params.get('id')
        testingCenterName = request.query_params.get('testingCenter__name')
        testingCenterAgencyId = request.query_params.get('testingCenter__testingCenterAgency__id')
        componentId = request.query_params.get('component__id')
        masRailComponentType__id = request.query_params.get('masRailComponentType__id')
        masTrainType__id = request.query_params.get('masTrainType__id')
        standardTypes = request.query_params.get('standardTypes')
        ordering = request.query_params.get('ordering')
        
        queryset = self.get_queryset()
        if masStandardAgency__code:
            queryset = queryset.filter(masStandardAgency__code=masStandardAgency__code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if code:
            queryset = queryset.filter(code__icontains=code)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if testingCenterId or testingCenterName or testingCenterAgencyId:
            standardTestingCenter = StandardTestingCenter.objects.filter(testingCenter__id=testingCenterId)
            if testingCenterId:
                queryset = queryset.filter(id__in=standardTestingCenter.values_list('standard__id', flat=True))
            if testingCenterName:
                queryset = queryset.filter(testingCenter__name__icontains=testingCenterName)
            if testingCenterAgencyId:
                queryset = queryset.filter(testingCenter__testingCenterAgency__id=testingCenterAgencyId)
        if standardId:
            queryset = queryset.filter(id=standardId)
        if id:
            queryset = queryset.filter(id=id)
        if componentId:
            standardComponent = StandardComponent.objects.filter(component__id=componentId)
            queryset = queryset.filter(id__in=standardComponent.values_list('standard__id', flat=True))
        if masTypeOfWork__id:
            queryset = queryset.filter(masTypeOfWork__id=masTypeOfWork__id)
        if masRailComponentType__id:
            queryset = queryset.filter(masRailComponentType__id=masRailComponentType__id)
        if masTrainType__id:
            queryset = queryset.filter(masTrainType__id=masTrainType__id)
        if standardTypes:
            standardTypeList = [int(standardType) for standardType in standardTypes.split(',')]
            standardMasStandardCategories = StandardMasStandardCategory.objects.filter(masStandardCategory__id__in=standardTypeList)
            queryset = queryset.filter(id__in=standardMasStandardCategories.values_list('standard__id', flat=True))
        if ordering:
            queryset = queryset.order_by(ordering)
        
        for item in queryset:
            if item.file == '-' or item.file == '':
                item.file = None
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            for item in serializer.data:
                standardMasStandardCategories = StandardMasStandardCategory.objects.filter(standard__id=item['id'])
                item['standardMasStandardCategories'] = StandardMasStandardCategorySerializer(standardMasStandardCategories, many=True).data
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data
        standardMasStandardCategories = StandardMasStandardCategory.objects.filter(standard=instance)
        data['standardMasStandardCategories'] = StandardMasStandardCategorySerializer(standardMasStandardCategories, many=True).data
        return Response(data)
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        standard = serializer.save()
        standardTypes = request.data.get('standardTypes', [])
        for standardType in standardTypes:
            StandardMasStandardCategory.objects.create(standard=standard, masStandardCategory=MasStandardCategory.objects.get(id=standardType))
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        standard = serializer.save()
        standardTypes = request.data.get('standardTypes', [])
        
        existing_categories = set(StandardMasStandardCategory.objects.filter(standard=standard).values_list('masStandardCategory__id', flat=True))
        new_categories = set(int(standardType) for standardType in standardTypes)
        
        categories_to_delete = existing_categories - new_categories
        if categories_to_delete:
            StandardMasStandardCategory.objects.filter(standard=standard, masStandardCategory__id__in=categories_to_delete).delete()
        
        categories_to_create = new_categories - existing_categories
        for standardType in categories_to_create:
            StandardMasStandardCategory.objects.create(standard=standard, masStandardCategory=MasStandardCategory.objects.get(id=standardType))
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลมาตรฐานระบบราง</h1>
        <p>Request Body:</p>
        <pre>
        {
            "ids": [1, 2, 3, ...]  # List of standard IDs to include in the export
        }
        </pre>
        """
        
        ids = request.data.get('ids')
        masStandardAgency__code = request.data.get('masStandardAgency__code')
        name = request.data.get('name')
        code = request.data.get('code')
        masTypeOfWork__id = request.data.get('masTypeOfWork__id')
        status = request.data.get('status')
        testingCenterId = request.data.get('testingCenter__id')
        standardId = request.data.get('standard__id')
        id = request.data.get('id')
        testingCenterName = request.data.get('testingCenter__name')
        testingCenterAgencyId = request.data.get('testingCenter__testingCenterAgency__id')
        componentId = request.data.get('component__id')
        masRailComponentType__id = request.data.get('masRailComponentType__id')
        masTrainType__id = request.data.get('masTrainType__id')
        standardTypes = request.data.get('standardTypes')       
        queryset = self.get_queryset()
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
        if masStandardAgency__code:
            queryset = queryset.filter(masStandardAgency__code=masStandardAgency__code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if code:
            queryset = queryset.filter(code__icontains=code)
        if status:
            queryset = queryset.filter(status=convert_str_to_bool(status))
        if testingCenterId or testingCenterName or testingCenterAgencyId:
            standardTestingCenter = StandardTestingCenter.objects.filter(testingCenter__id=testingCenterId)
            if testingCenterId:
                queryset = queryset.filter(id__in=standardTestingCenter.values_list('standard__id', flat=True))
            if testingCenterName:
                queryset = queryset.filter(testingCenter__name__icontains=testingCenterName)
            if testingCenterAgencyId:
                queryset = queryset.filter(testingCenter__testingCenterAgency__id=testingCenterAgencyId)
        if standardId:
            queryset = queryset.filter(id=standardId)
        if id:
            queryset = queryset.filter(id=id)
        if componentId:
            standardComponent = StandardComponent.objects.filter(component__id=componentId)
            queryset = queryset.filter(id__in=standardComponent.values_list('standard__id', flat=True))
        if masTypeOfWork__id:
            queryset = queryset.filter(masTypeOfWork__id=masTypeOfWork__id)
        if masRailComponentType__id:
            queryset = queryset.filter(masRailComponentType__id=masRailComponentType__id)
        if masTrainType__id:
            queryset = queryset.filter(masTrainType__id=masTrainType__id)
        if standardTypes:
            standardTypeList = [int(standardType) for standardType in standardTypes.split(',')]
            standardMasStandardCategories = StandardMasStandardCategory.objects.filter(masStandardCategory__id__in=standardTypeList)
            queryset = queryset.filter(id__in=standardMasStandardCategories.values_list('standard__id', flat=True))
        
        for item in queryset:
            try:
                if item.masTrainType_id:
                    masTrainType = MasTrainType.objects.get(id=item.masTrainType_id)
                    item.masTrainTypeName = masTrainType.name
                else:
                    item.masTrainTypeName = None
                if item.masRailComponentType_id:
                    masRailComponentType = MasRailComponentType.objects.get(id=item.masRailComponentType_id)
                    item.masRailComponentTypeName = masRailComponentType.name
                else:
                    item.masRailComponentTypeName = None
                if item.masTypeOfWork_id:
                    masTypeOfWork = MasTypeOfWork.objects.get(id=item.masTypeOfWork_id)
                    item.masTypeOfWorkName = masTypeOfWork.name
                else:
                    item.masTypeOfWorkName = None
                if item.masStandardAgency_id:
                    masStandardAgency = MasStandardAgency.objects.get(id=item.masStandardAgency_id)
                    item.masStandardAgencyName = masStandardAgency.name
                else:
                    item.masStandardAgencyName = None
            except MasTrainType.DoesNotExist:
                item.masTrainTypeName = None
            except MasRailComponentType.DoesNotExist:
                item.masRailComponentTypeName = None
            except MasTypeOfWork.DoesNotExist:
                item.masTypeOfWorkName = None
            except MasStandardAgency.DoesNotExist:
                item.masStandardAgencyName = None

            # Handle updateUser
            try:
                item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            except User.DoesNotExist:
                item.updateUser = None

            # Handle createUser
            try:
                item.createUser = User.objects.get(id=item.createUserId) if item.createUserId else None
            except User.DoesNotExist:
                item.createUser = None
            
        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'รหัสมาตรฐาน': item.code,
                'ชื่อมาตรฐาน': item.name,
                'รายละเอียดมาตรฐาน': item.detail,
                'ประเภทรถไฟ': item.masTrainTypeName,
                'ประเภทองค์ประกอบของราง': item.masRailComponentTypeName,
                'ประเภทงาน': item.masTypeOfWorkName,
                'ปีที่จัดทำ': item.year,
                'หน่วยงานที่จัดทำมาตรฐาน': item.masStandardAgencyName,
                'ลิงก์มาตรฐาน': item.link,
                'จำนวนเข้าชม': item.views,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1
        
        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลมาตรฐานระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:M1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:M2', thai_date, date_format)
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response

    @action(detail=False, methods=['PATCH'], url_path='update-views/(?P<pk>[^/.]+)', permission_classes=[AllowAny])
    def update_views(self, request, pk, *args, **kwargs):
        instance = self.get_queryset().get(id=pk)
        instance.views += 1
        instance.save()
        return Response({"message": "Views updated successfully."}, status=status.HTTP_200_OK)

@extend_schema(
    tags=["Master Data"]
)
class StandardMasStandardCategoryViewSet(viewsets.ModelViewSet):
    queryset = StandardMasStandardCategory.objects.all()
    serializer_class = StandardMasStandardCategorySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['standard__id', 'masStandardCategory__id']
