# Maintenance App

This Django app provides API endpoints for managing maintenance work records.

## Models

### MaintenanceWork
- **code**: Maintenance work code (<PERSON><PERSON><PERSON><PERSON>, max_length=200)
- **systemComponent**: System component being maintained (Cha<PERSON><PERSON><PERSON>, max_length=500)
- **maintenanceType**: Type of maintenance (<PERSON><PERSON><PERSON><PERSON>, max_length=1)
- **scheduleDate**: Scheduled date for maintenance (DateTimeField)
- **actualDate**: Actual date maintenance was performed (DateTimeField, optional)
- **technician**: Technician assigned (Char<PERSON><PERSON>, max_length=500, optional)
- **status**: Maintenance status (BooleanField)
- **costEstimate**: Estimated cost (FloatField)
- **remark**: Additional remarks (TextField, optional)

Inherits from `BaseModel` which provides:
- **id**: Primary key
- **createUserId**: ID of user who created the record
- **createDate**: Creation timestamp
- **updateUserId**: ID of user who last updated the record
- **updateDate**: Last update timestamp
- **createUser**: User object who created the record (property)
- **updateUser**: User object who last updated the record (property)

## API Endpoints

### MaintenanceWork ViewSet
**Base URL**: `/api/maintenance-work/`

**Available endpoints**:
- `GET /api/maintenance-work/` - List all maintenance work records
- `POST /api/maintenance-work/` - Create a new maintenance work record
- `GET /api/maintenance-work/{id}/` - Retrieve a specific maintenance work record
- `PUT /api/maintenance-work/{id}/` - Update a maintenance work record
- `PATCH /api/maintenance-work/{id}/` - Partially update a maintenance work record
- `DELETE /api/maintenance-work/{id}/` - Delete a maintenance work record

**Features**:
- **Pagination**: Uses CustomPagination (10 items per page by default)
- **Authentication**: JWT Authentication required for write operations
- **Permissions**: Read-only access for unauthenticated users, full CRUD for authenticated users
- **Filtering**: Filter by code, systemComponent, maintenanceType, status, technician
- **Search**: Search in code, systemComponent, technician, remark fields
- **Ordering**: Order by code, scheduleDate, actualDate, costEstimate, createDate, updateDate
- **Default ordering**: Most recently created first (-createDate)

## Usage Examples

### List maintenance work records
```bash
GET /api/maintenance-work/
```

### Filter by status
```bash
GET /api/maintenance-work/?status=true
```

### Search for specific component
```bash
GET /api/maintenance-work/?search=engine
```

### Order by schedule date
```bash
GET /api/maintenance-work/?ordering=scheduleDate
```

### Create new maintenance work record
```bash
POST /api/maintenance-work/
Content-Type: application/json

{
    "code": "MAINT-001",
    "systemComponent": "Engine Block",
    "maintenanceType": "P",
    "scheduleDate": "2024-01-15T10:00:00Z",
    "status": false,
    "costEstimate": 1500.00,
    "remark": "Routine preventive maintenance"
}
```

## Testing

Run tests with:
```bash
python manage.py test maintenance
```

## Files Structure

```
maintenance/
├── __init__.py
├── admin.py
├── apps.py
├── models.py          # MaintenanceWork model
├── serializers.py     # MaintenanceWorkSerializer
├── views.py           # MaintenanceWorkViewSet
├── tests.py           # Test cases
├── README.md          # This file
└── migrations/
    └── __init__.py
```
