"""
Unit Tests for MongoDB Integration

This module contains comprehensive tests for MongoDB CRUD operations,
API endpoints, and document models.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from bson import ObjectId
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model

from utils.mongodb_crud import MongoDBCRUD
from utils.mongodb_connection import MongoDBConnectionManager
from utils.mongodb_exceptions import (
    MongoDBConnectionError, MongoDBValidationError,
    MongoDBOperationError, MongoDBDocumentNotFoundError
)
from .models import MongoUser, MongoLog, MongoAnalytics, MongoConfiguration
from .serializers import (
    MongoUserSerializer, MongoLogSerializer,
    MongoAnalyticsSerializer, MongoConfigurationSerializer
)

User = get_user_model()


class MongoDBConnectionManagerTests(TestCase):
    """Test cases for MongoDB connection manager."""

    @patch('utils.mongodb_connection.MongoClient')
    def test_connection_manager_singleton(self, mock_mongo_client):
        """Test that connection manager is a singleton."""
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client

        manager1 = MongoDBConnectionManager()
        manager2 = MongoDBConnectionManager()

        self.assertIs(manager1, manager2)

    @patch('utils.mongodb_connection.MongoClient')
    def test_successful_connection(self, mock_mongo_client):
        """Test successful MongoDB connection."""
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client
        mock_client.admin.command.return_value = {'ok': 1}

        manager = MongoDBConnectionManager()

        self.assertIsNotNone(manager._client)
        mock_client.admin.command.assert_called_with('ping')

    @patch('utils.mongodb_connection.MongoClient')
    def test_connection_failure(self, mock_mongo_client):
        """Test MongoDB connection failure."""
        from pymongo.errors import ConnectionFailure
        mock_mongo_client.side_effect = ConnectionFailure("Connection failed")

        with self.assertRaises(ConnectionFailure):
            MongoDBConnectionManager()

    @patch('utils.mongodb_connection.MongoClient')
    def test_test_connection(self, mock_mongo_client):
        """Test connection testing functionality."""
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client
        mock_client.admin.command.return_value = {'ok': 1}

        manager = MongoDBConnectionManager()

        # Test successful connection
        self.assertTrue(manager.test_connection())

        # Test failed connection
        mock_client.admin.command.side_effect = Exception("Connection lost")
        self.assertFalse(manager.test_connection())


class MongoDBCRUDTests(TestCase):
    """Test cases for MongoDB CRUD operations."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_collection = Mock()

        with patch('utils.mongodb_crud.get_mongodb_collection') as mock_get_collection:
            mock_get_collection.return_value = self.mock_collection
            self.crud = MongoDBCRUD('test_collection')

    def test_create_one(self):
        """Test creating a single document."""
        mock_result = Mock()
        mock_result.inserted_id = ObjectId()
        self.mock_collection.insert_one.return_value = mock_result

        document = {'name': 'test', 'value': 123}
        result = self.crud.create_one(document)

        self.assertEqual(result, mock_result)
        self.mock_collection.insert_one.assert_called_once()

        # Check that timestamps were added
        call_args = self.mock_collection.insert_one.call_args[0][0]
        self.assertIn('created_at', call_args)
        self.assertIn('updated_at', call_args)

    def test_find_by_id(self):
        """Test finding a document by ID."""
        test_id = ObjectId()
        mock_document = {'_id': test_id, 'name': 'test'}
        self.mock_collection.find_one.return_value = mock_document

        result = self.crud.find_by_id(str(test_id))

        self.assertEqual(result['_id'], str(test_id))
        self.mock_collection.find_one.assert_called_once_with({'_id': test_id}, None)

    def test_find_by_invalid_id(self):
        """Test finding a document with invalid ID."""
        with self.assertRaises(ValueError):
            self.crud.find_by_id('invalid_id')

    def test_update_by_id(self):
        """Test updating a document by ID."""
        test_id = ObjectId()
        mock_result = Mock()
        mock_result.matched_count = 1
        mock_result.modified_count = 1
        self.mock_collection.update_one.return_value = mock_result

        update_data = {'$set': {'name': 'updated'}}
        result = self.crud.update_by_id(str(test_id), update_data)

        self.assertEqual(result, mock_result)
        self.mock_collection.update_one.assert_called_once()

        # Check that updated_at timestamp was added
        call_args = self.mock_collection.update_one.call_args[0][1]
        self.assertIn('updated_at', call_args['$set'])

    def test_delete_by_id(self):
        """Test deleting a document by ID."""
        test_id = ObjectId()
        mock_result = Mock()
        mock_result.deleted_count = 1
        self.mock_collection.delete_one.return_value = mock_result

        result = self.crud.delete_by_id(str(test_id))

        self.assertEqual(result, mock_result)
        self.mock_collection.delete_one.assert_called_once_with({'_id': test_id})

    def test_count_documents(self):
        """Test counting documents."""
        self.mock_collection.count_documents.return_value = 5

        count = self.crud.count_documents({'status': 'active'})

        self.assertEqual(count, 5)
        self.mock_collection.count_documents.assert_called_once_with({'status': 'active'})


class MongoDocumentModelTests(TestCase):
    """Test cases for MongoDB document models."""

    def test_mongo_user_creation(self):
        """Test MongoUser document creation."""
        user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'user_type': 'regular'
        }

        user = MongoUser(**user_data)

        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.full_name, 'Test User')
        self.assertTrue(user.is_active)

    def test_mongo_user_validation(self):
        """Test MongoUser validation."""
        # Valid user
        valid_user = MongoUser(
            username='testuser',
            email='<EMAIL>',
            user_type='regular'
        )
        errors = valid_user.validate()
        self.assertEqual(len(errors), 0)

        # Invalid user - missing required fields
        invalid_user = MongoUser(user_type='invalid_type')
        errors = invalid_user.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn('Username is required', errors)
        self.assertIn('Email is required', errors)
        self.assertIn('Invalid user type', errors)

    def test_mongo_user_to_dict(self):
        """Test MongoUser to_dict conversion."""
        user = MongoUser(
            username='testuser',
            email='<EMAIL>',
            profile_data={'department': 'IT'}
        )

        user_dict = user.to_dict()

        self.assertEqual(user_dict['username'], 'testuser')
        self.assertEqual(user_dict['email'], '<EMAIL>')
        self.assertEqual(user_dict['profile_data'], {'department': 'IT'})
        self.assertIn('created_at', user_dict)

    def test_mongo_log_validation(self):
        """Test MongoLog validation."""
        # Valid log
        valid_log = MongoLog(
            level='INFO',
            message='Test message'
        )
        errors = valid_log.validate()
        self.assertEqual(len(errors), 0)

        # Invalid log
        invalid_log = MongoLog(level='INVALID')
        errors = invalid_log.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn('Message is required', errors)
        self.assertIn('Invalid log level', errors)

    def test_mongo_analytics_validation(self):
        """Test MongoAnalytics validation."""
        # Valid analytics
        valid_analytics = MongoAnalytics(
            event_type='page_view',
            event_name='dashboard_view'
        )
        errors = valid_analytics.validate()
        self.assertEqual(len(errors), 0)

        # Invalid analytics
        invalid_analytics = MongoAnalytics()
        errors = invalid_analytics.validate()
        self.assertGreater(len(errors), 0)
        self.assertIn('Event type is required', errors)
        self.assertIn('Event name is required', errors)


class MongoSerializerTests(TestCase):
    """Test cases for MongoDB serializers."""

    def test_mongo_user_serializer_validation(self):
        """Test MongoUserSerializer validation."""
        # Valid data
        valid_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'user_type': 'regular'
        }

        serializer = MongoUserSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        # Invalid data
        invalid_data = {
            'username': 'ab',  # Too short
            'email': 'invalid-email',
            'user_type': 'invalid'
        }

        serializer = MongoUserSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('username', serializer.errors)
        self.assertIn('email', serializer.errors)
        self.assertIn('user_type', serializer.errors)

    def test_mongo_log_serializer_validation(self):
        """Test MongoLogSerializer validation."""
        # Valid data
        valid_data = {
            'level': 'INFO',
            'message': 'Test log message',
            'response_status': 200
        }

        serializer = MongoLogSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        # Invalid data
        invalid_data = {
            'level': 'INVALID',
            'message': '',
            'response_status': 999
        }

        serializer = MongoLogSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('level', serializer.errors)
        self.assertIn('message', serializer.errors)
        self.assertIn('response_status', serializer.errors)


class MongoAPITests(APITestCase):
    """Test cases for MongoDB API endpoints."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test user for authentication
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            firstname='Test',
            lastname='User'
        )
        self.client.force_authenticate(user=self.user)

        # Mock MongoDB operations
        self.mock_crud_patcher = patch('mongodb_app.views.MongoDBCRUD')
        self.mock_crud_class = self.mock_crud_patcher.start()
        self.mock_crud = Mock()
        self.mock_crud_class.return_value = self.mock_crud

    def tearDown(self):
        """Clean up test fixtures."""
        self.mock_crud_patcher.stop()

    def test_mongo_user_list(self):
        """Test listing MongoDB users."""
        # Mock data
        mock_users = [
            {
                '_id': '507f1f77bcf86cd799439011',
                'username': 'user1',
                'email': '<EMAIL>',
                'created_at': datetime.utcnow().isoformat()
            },
            {
                '_id': '507f1f77bcf86cd799439012',
                'username': 'user2',
                'email': '<EMAIL>',
                'created_at': datetime.utcnow().isoformat()
            }
        ]

        self.mock_crud.find_many.return_value = mock_users
        self.mock_crud.count_documents.return_value = 2

        url = reverse('mongo-users-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        self.assertEqual(response.data['total_count'], 2)

    def test_mongo_user_create(self):
        """Test creating a MongoDB user."""
        user_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'user_type': 'regular'
        }

        # Mock successful creation
        mock_result = Mock()
        mock_result.inserted_id = ObjectId()
        self.mock_crud.create_one.return_value = mock_result

        created_user = user_data.copy()
        created_user['_id'] = str(mock_result.inserted_id)
        created_user['created_at'] = datetime.utcnow().isoformat()
        self.mock_crud.find_by_id.return_value = created_user

        url = reverse('mongo-users-list')
        response = self.client.post(url, user_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['username'], 'newuser')
        self.assertEqual(response.data['email'], '<EMAIL>')

    def test_mongo_user_create_invalid_data(self):
        """Test creating a MongoDB user with invalid data."""
        invalid_data = {
            'username': 'ab',  # Too short
            'email': 'invalid-email',
            'user_type': 'invalid'
        }

        url = reverse('mongo-users-list')
        response = self.client.post(url, invalid_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', response.data)
        self.assertIn('email', response.data)
        self.assertIn('user_type', response.data)

    def test_mongo_user_retrieve(self):
        """Test retrieving a specific MongoDB user."""
        user_id = '507f1f77bcf86cd799439011'
        mock_user = {
            '_id': user_id,
            'username': 'testuser',
            'email': '<EMAIL>',
            'created_at': datetime.utcnow().isoformat()
        }

        self.mock_crud.find_by_id.return_value = mock_user

        url = reverse('mongo-users-detail', kwargs={'pk': user_id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['_id'], user_id)
        self.assertEqual(response.data['username'], 'testuser')

    def test_mongo_user_retrieve_not_found(self):
        """Test retrieving a non-existent MongoDB user."""
        user_id = '507f1f77bcf86cd799439011'
        self.mock_crud.find_by_id.return_value = None

        url = reverse('mongo-users-detail', kwargs={'pk': user_id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_mongo_user_update(self):
        """Test updating a MongoDB user."""
        user_id = '507f1f77bcf86cd799439011'
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name'
        }

        # Mock existing user
        existing_user = {
            '_id': user_id,
            'username': 'testuser',
            'email': '<EMAIL>'
        }
        self.mock_crud.find_by_id.return_value = existing_user

        # Mock update result
        mock_result = Mock()
        mock_result.matched_count = 1
        mock_result.modified_count = 1
        self.mock_crud.update_by_id.return_value = mock_result

        # Mock updated user
        updated_user = existing_user.copy()
        updated_user.update(update_data)
        self.mock_crud.find_by_id.return_value = updated_user

        url = reverse('mongo-users-detail', kwargs={'pk': user_id})
        response = self.client.put(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated')
        self.assertEqual(response.data['last_name'], 'Name')

    def test_mongo_user_delete(self):
        """Test deleting a MongoDB user."""
        user_id = '507f1f77bcf86cd799439011'

        # Mock successful deletion
        mock_result = Mock()
        mock_result.deleted_count = 1
        self.mock_crud.delete_by_id.return_value = mock_result

        url = reverse('mongo-users-detail', kwargs={'pk': user_id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_mongo_user_active_users_action(self):
        """Test getting active users."""
        mock_active_users = [
            {
                '_id': '507f1f77bcf86cd799439011',
                'username': 'activeuser1',
                'is_active': True
            },
            {
                '_id': '507f1f77bcf86cd799439012',
                'username': 'activeuser2',
                'is_active': True
            }
        ]

        self.mock_crud.find_many.return_value = mock_active_users

        url = reverse('mongo-users-active-users')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        # Verify filter was called with is_active: True
        self.mock_crud.find_many.assert_called_once_with(filter_dict={'is_active': True})
